<?xml version="1.0"?>
<!-- edited with XMLSpy v2011 (http://www.altova.com) by mcj (mcj) -->
<EtherCATInfo xmlns:xsi="http://www.v-t.net.cn" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.0">
	<Vendor>
		<Id>#x556666</Id>
		<Name>Shenzhen V and T Technology Co.,LTD</Name>
		<ImageData16x14>424DE6000000000000007600000028000000100000000E000000010004000000000070000000000000000000000000000000000000000000000000008000008000000080800080000000800080008080000080808000C0C0C0000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6FF6FF6FF6FFFFFFFFFFFFFFFFFFFFFFFF666666FFFFFFF666FFFFFF666FF66FFFFFFFFFFFF66FFFFFFFFFFFFFFFFFFF2FFFFFFFF2FFFFF2F2FFFFFFF2FFFF2FFF2FFFFFF2FFFF2FFF2FFF2FF2FF222FFF22FF2222222FFFFFFFFFFFFFFFF</ImageData16x14>
	</Vendor>
	<Descriptions>
		<Groups>
			<Group>
				<Type>Drive</Type>
				<Name LcId="1033">Drives</Name>
				<Image16x14>DRIVE</Image16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x9D08" RevisionNo="#x783978D0">cspcsv</Type>
				<Name LcId="1033">cspcsv</Name>
				<Info>
					<StateMachine>
						<Timeout>
							<PreopTimeout>2000</PreopTimeout>
							<SafeopOpTimeout>15000</SafeopOpTimeout>
							<BackToInitTimeout>5000</BackToInitTimeout>
							<BackToSafeopTimeout>200</BackToSafeopTimeout>
						</Timeout>
					</StateMachine>
					<Mailbox>
						<Timeout>
							<RequestTimeout>100</RequestTimeout>
							<ResponseTimeout>2000</ResponseTimeout>
						</Timeout>
					</Mailbox>
				</Info>
				<GroupType>Drive</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>BIT2</Name>
								<BitSize>2</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>BOOL</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>ARRAY [0..3] OF BYTE</Name>
								<BaseType>USINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>0</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<!--Device Name (0x1008) type-->
								<Name>STRING(34)</Name>
								<BitSize>272</BitSize>
							</DataType>
							<DataType>
								<!--Hardware version string (0x1009)-->
								<Name>STRING(4)</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Ident object (0x1018) type-->
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00ARR</Name>
								<BaseType>USINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C00ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Error Setting object (0x10F1) type-->
								<Name>DT10F1</Name>
								<BitSize>64</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Local Error Reaction</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Sync Error Counter Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Datatype for SM2(Output) Synchronisation  Parameter-->
								<Name>DT1C32</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Datatype for SM3(Input) Synchronisation  Parameter-->
								<Name>DT1C33</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C12ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C13ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C13</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C13ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1600ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1601ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1601</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1601ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1602ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1602</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1602ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1603ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1603</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1603ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1604ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1604</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1604ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1605ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1605</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1605ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1606ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1606</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1606ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1607ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1607</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1607ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A00ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A01ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A01</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A01ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A02ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A02</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A02ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A03ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A03</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A03ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A04ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A04</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A04ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A05ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A05</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A05ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A06ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A06</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A06ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A07ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A07</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of mapped objects</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A07ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<Backup>1</Backup>
										<Setting>1</Setting>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DTF000</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Module index distance</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Maximum number of modules</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DTF010ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DTF010</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DTF010ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<!--Module Profile Information-->
							</DataType>
							<DataType>
								<Name>DTF050ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<!--OB2000-->
								<Name>DT2000</Name>
								<BitSize>640</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P0.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P0.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P0.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P0.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P0.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P0.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P0.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P0.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P0.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P0.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P0.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P0.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P0.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P0.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P0.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>P0.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>P0.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>P0.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>P0.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>P0.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>P0.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>P0.21</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>P0.22</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>P0.23</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>P0.24</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>P0.25</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>P0.26</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>P0.27</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>P0.28</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>P0.29</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>P0.30</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>P0.31</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>P0.32</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>P0.33</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>35</SubIdx>
									<Name>P0.34</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>36</SubIdx>
									<Name>P0.35</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>37</SubIdx>
									<Name>P0.36</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>38</SubIdx>
									<Name>P0.37</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>P0.38</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2001-->
								<Name>DT2001</Name>
								<BitSize>256</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P1.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P1.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P1.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P1.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P1.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P1.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P1.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P1.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P1.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P1.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P1.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P1.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P1.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P1.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P1.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2002-->
								<Name>DT2002</Name>
								<BitSize>416</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P2.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P2.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P2.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P2.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P2.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P2.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P2.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P2.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P2.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P2.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P2.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P2.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P2.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P2.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P2.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>P2.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>P2.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>P2.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>P2.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>P2.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>P2.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>P2.21</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>P2.22</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>P2.23</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>P2.24</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2003-->
								<Name>DT2003</Name>
								<BitSize>288</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P3.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P3.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P3.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P3.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P3.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P3.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P3.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P3.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P3.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P3.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P3.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P3.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P3.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P3.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P3.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>P3.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>P3.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2004-->
								<Name>DT2004</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P4.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P4.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P4.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P4.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P4.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P4.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P4.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P4.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P4.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P4.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P4.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P4.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P4.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P4.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P4.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>P4.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2005-->
								<Name>DT2005</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P5.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P5.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P5.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P5.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P5.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P5.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P5.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P5.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P5.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P5.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P5.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P5.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P5.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P5.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>P5.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>P5.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2006-->
								<Name>DT2006</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P6.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P6.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P6.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P6.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P6.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P6.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P6.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P6.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2007-->
								<Name>DT2007</Name>
								<BitSize>240</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P7.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P7.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P7.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P7.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P7.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P7.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P7.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P7.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P7.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P7.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P7.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P7.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>P7.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>P7.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2008-->
								<Name>DT2008</Name>
								<BitSize>176</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P8.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P8.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P8.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P8.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P8.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P8.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P8.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P8.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P8.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P8.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB2009-->
								<Name>DT2009</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>P9.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>P9.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>P9.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>P9.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>P9.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>P9.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>P9.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>P9.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>P9.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>P9.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>P9.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>P9.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200A-->
								<Name>DT200A</Name>
								<BitSize>256</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>PA.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PA.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>PA.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PA.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>PA.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PA.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>PA.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>PA.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>PA.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>PA.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>PA.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>PA.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>PA.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>PA.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>PA.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200B-->
								<Name>DT200B</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>PB.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PB.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>PB.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PB.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>PB.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PB.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200C-->
								<Name>DT200C</Name>
								<BitSize>912</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>PC.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PC.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>PC.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PC.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>PC.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PC.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>PC.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>PC.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>PC.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>PC.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>PC.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>PC.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>PC.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>PC.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>PC.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>PC.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>PC.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>PC.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>PC.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>PC.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>PC.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>PC.21</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>PC.22</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>PC.23</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>PC.24</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>PC.25</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>PC.26</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>PC.27</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>PC.28</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>PC.29</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>PC.30</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>PC.31</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>PC.32</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>PC.33</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>35</SubIdx>
									<Name>PC.34</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>36</SubIdx>
									<Name>PC.35</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>37</SubIdx>
									<Name>PC.36</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>38</SubIdx>
									<Name>PC.37</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>PC.38</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>40</SubIdx>
									<Name>PC.39</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>640</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>41</SubIdx>
									<Name>PC.40</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>42</SubIdx>
									<Name>PC.41</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>672</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>43</SubIdx>
									<Name>PC.42</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>44</SubIdx>
									<Name>PC.43</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>704</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>45</SubIdx>
									<Name>PC.44</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>46</SubIdx>
									<Name>PC.45</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>736</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>47</SubIdx>
									<Name>PC.46</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>48</SubIdx>
									<Name>PC.47</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>768</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>49</SubIdx>
									<Name>PC.48</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>784</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>50</SubIdx>
									<Name>PC.49</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>800</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>51</SubIdx>
									<Name>PC.50</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>816</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>52</SubIdx>
									<Name>PC.51</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>832</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>53</SubIdx>
									<Name>PC.52</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>848</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>54</SubIdx>
									<Name>PC.53</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>864</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>55</SubIdx>
									<Name>PC.54</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>880</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>56</SubIdx>
									<Name>PC.55</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>896</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200D-->
								<Name>DT200D</Name>
								<BitSize>560</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>PD.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PD.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>PD.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PD.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>PD.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PD.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>PD.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>PD.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>PD.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>PD.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>PD.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>PD.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>PD.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>PD.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>PD.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>PD.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>PD.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>PD.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>PD.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>PD.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>PD.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>PD.21</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>PD.22</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>PD.23</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>PD.24</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>PD.25</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>PD.26</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>PD.27</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>PD.28</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>PD.29</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>PD.30</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>PD.31</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>PD.32</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>PD.33</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200E-->
								<Name>DT200E</Name>
								<BitSize>352</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>PE.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PE.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>PE.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PE.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>PE.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PE.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>PE.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>PE.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>PE.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>PE.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>PE.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>PE.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>PE.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>PE.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>PE.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>PE.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>PE.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>PE.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>PE.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>PE.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>PE.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--OB200F-->
								<Name>DT200F</Name>
								<BitSize>912</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>C0.00</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>C0.01</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>C0.02</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>C0.03</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>C0.04</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>C0.05</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>C0.06</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>C0.07</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>C0.08</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>C0.09</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>C0.10</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>C0.11</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>C0.12</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>C0.13</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>C0.14</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>C0.15</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>C0.16</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>C0.17</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>C0.18</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>C0.19</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>C0.20</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>C0.21</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>C0.22</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>C0.23</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>C0.24</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>C0.25</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>C0.26</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>C0.27</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>C0.28</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>C0.29</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>C0.30</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>C0.31</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>C0.32</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>C0.33</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>35</SubIdx>
									<Name>C0.34</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>36</SubIdx>
									<Name>C0.35</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>37</SubIdx>
									<Name>C0.36</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>38</SubIdx>
									<Name>C0.37</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>C0.38</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>40</SubIdx>
									<Name>C0.39</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>640</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>41</SubIdx>
									<Name>C0.40</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>42</SubIdx>
									<Name>C0.41</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>672</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>43</SubIdx>
									<Name>C0.42</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>44</SubIdx>
									<Name>C0.43</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>704</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>45</SubIdx>
									<Name>C0.44</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>46</SubIdx>
									<Name>C0.45</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>736</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>47</SubIdx>
									<Name>C0.46</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>48</SubIdx>
									<Name>C0.47</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>768</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>49</SubIdx>
									<Name>C0.48</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>784</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>50</SubIdx>
									<Name>C0.49</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>800</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>51</SubIdx>
									<Name>C0.50</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>816</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>52</SubIdx>
									<Name>C0.51</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>832</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>53</SubIdx>
									<Name>C0.52</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>848</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>54</SubIdx>
									<Name>C0.53</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>864</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>55</SubIdx>
									<Name>C0.54</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>880</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>56</SubIdx>
									<Name>C0.55</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>896</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DTF050</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DTF050ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<!--Module Ident list of the detected modules-->
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010200</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Device name</Name>
								<Type>STRING(34)</Type>
								<BitSize>272</BitSize>
								<Info>
									<DefaultData>454C39383030207C203241786973204369413430322053616D706C655F5635693131</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Hardware version</Name>
								<Type>STRING(4)</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>6E2E612E</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100a</Index>
								<Name>Software version</Name>
								<Type>STRING(4)</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>352E3131</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c00</Index>
								<Name>Sync manager type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vendor ID</Name>
										<Info>
											<DefaultData>020000E0</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Product code</Name>
										<Info>
											<DefaultData>52304826</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Revision</Name>
										<Info>
											<DefaultData>11020200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial number</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x10F1</Index>
								<Name>Error Settings</Name>
								<Type>DT10F1</Type>
								<BitSize>64</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Local Error Reaction</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error Counter Limit</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>0780</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and Copy Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get Cycle Time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>0780</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and Copy Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get Cycle Time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c12</Index>
								<Name>RxPDO assign</Name>
								<Type>DT1C12</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c13</Index>
								<Name>TxPDO assign</Name>
								<Type>DT1C13</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>ControlWord</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>#x0</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>StatusWord</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>#x0</DefaultValue>
								</Info>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultValue>#x0</DefaultValue>
								</Info>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60ff</Index>
								<Name>Target Velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultValue>#x0</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile Velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultValue>#x0</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x2000</Index>
								<Name>P0.xx</Name>
								<Type>DT2000</Type>
								<BitSize>640</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.21</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.22</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.23</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.24</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.25</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.26</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.27</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.28</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.29</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.30</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.31</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.32</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.33</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.34</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.35</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.36</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.37</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P0.38</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2001</Index>
								<Name>P1.xx</Name>
								<Type>DT2001</Type>
								<BitSize>256</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P1.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2002</Index>
								<Name>P2.xx</Name>
								<Type>DT2002</Type>
								<BitSize>416</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.21</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.22</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.23</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P2.24</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2003</Index>
								<Name>P3.xx</Name>
								<Type>DT2003</Type>
								<BitSize>288</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P3.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2004</Index>
								<Name>P4.xx</Name>
								<Type>DT2004</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P4.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>P5.xx</Name>
								<Type>DT2005</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P5.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>P6.xx</Name>
								<Type>DT2006</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P6.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>P7.xx</Name>
								<Type>DT2007</Type>
								<BitSize>240</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P7.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2008</Index>
								<Name>P8.xx</Name>
								<Type>DT2008</Type>
								<BitSize>176</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P8.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2009</Index>
								<Name>P9.xx</Name>
								<Type>DT2009</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>P9.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200A</Index>
								<Name>PA.xx</Name>
								<Type>DT200A</Type>
								<BitSize>256</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PA.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200B</Index>
								<Name>PB.xx</Name>
								<Type>DT200B</Type>
								<BitSize>112</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PB.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200C</Index>
								<Name>PC.xx</Name>
								<Type>DT200C</Type>
								<BitSize>912</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.21</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.22</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.23</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.24</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.25</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.26</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.27</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.28</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.29</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.30</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.31</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.32</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.33</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.34</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.35</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.36</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.37</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.38</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.39</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.40</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.41</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.42</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.43</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.44</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.45</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.46</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.47</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.48</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.49</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.50</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.51</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.52</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.53</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.54</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PC.55</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200D</Index>
								<Name>PD.xx</Name>
								<Type>DT200D</Type>
								<BitSize>560</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.21</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.22</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.23</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.24</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.25</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.26</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.27</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.28</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.29</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.30</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.31</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.32</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PD.33</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200E</Index>
								<Name>PE.xx</Name>
								<Type>DT200E</Type>
								<BitSize>352</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PE.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200F</Index>
								<Name>C0.xx</Name>
								<Type>DT200F</Type>
								<BitSize>912</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.00</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.01</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.02</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.03</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.04</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.05</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.06</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.07</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.08</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.09</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.10</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.11</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.12</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.13</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.14</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.15</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.16</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.17</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.18</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.19</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.20</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.21</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.22</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.23</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.24</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.25</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.26</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.27</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.28</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.29</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.30</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.31</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.32</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.33</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.34</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.35</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.36</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.37</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.38</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.39</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.40</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.41</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.42</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.43</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.44</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.45</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.46</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.47</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.48</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.49</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.50</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.51</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.52</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.53</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.54</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>C0.55</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf000</Index>
								<Name>Modular device profile</Name>
								<Type>DTF000</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Module index distance</Name>
										<Info>
											<DefaultData>1000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Maximum number of modules</Name>
										<Info>
											<DefaultData>0300</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf010</Index>
								<Name>Module profile list</Name>
								<Type>DTF010</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>92010200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf050</Index>
								<Name>Module detected list</Name>
								<Type>DTF050</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>319800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Fmmu>MBoxState</Fmmu>
				<Sm MinSize="34" MaxSize="128" DefaultSize="128" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="34" MaxSize="128" DefaultSize="128" StartAddress="#x1080" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="0" StartAddress="#x1100" ControlByte="#x64" Enable="1">Outputs</Sm>
				<Sm DefaultSize="0" StartAddress="#x1400" ControlByte="#x20" Enable="1">Inputs</Sm>
				<RxPdo Sm="2">
					<Index>#x1600</Index>
					<Name>RxPDO csp/csv</Name>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>ControlWord</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Modes Of Operation</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x60b8</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch probe function</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x2105</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Reserve</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x2106</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Reserve</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x2107</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Reserve</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Sm="3">
					<Index>#x1A00</Index>
					<Name>TxPDO csp/csv</Name>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>StatusWord</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Modes Of Operation Display</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6078</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Current Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x60b9</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch probe status</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60ba</Index>
						<SubIndex>#x0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch probe pos1 pos value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox DataLinkLayer="1">
					<CoE SdoInfo="1" SegmentedSdo="1" CompleteAccess="0" PdoAssign="1" PdoConfig="0" PdoUpload="0">
						<InitCmd>
							<Transition>SO</Transition>
							<Index>#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</CoE>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC</Name>
						<Desc>DC-Synchron</Desc>
						<AssignActivate>#x300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="1">0</CycleTimeSync1>
					</OpMode>
					<OpMode>
						<Name>Synchron</Name>
						<Desc>SM-Synchron</Desc>
						<AssignActivate>#x0</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="1">0</CycleTimeSync1>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050E03440A0000000000</ConfigData>
				</Eeprom>
				<Image16x14>DRIVE</Image16x14>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>
