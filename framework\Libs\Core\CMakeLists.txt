﻿project(Core)

string(<PERSON><PERSON><PERSON> REPLACE "(.*)/(.*)/(.*)" "\\1" PROJECT_CTK_DIR  ${PROJECT_SOURCE_DIR})

set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)
set(BUILD_SHARED_LIBS ON)

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${PROJECT_CTK_DIR}/bin/cmake/debug)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${PROJECT_CTK_DIR}/bin/cmake/release)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${PROJECT_CTK_DIR}/bin/cmake/debug)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${PROJECT_CTK_DIR}/bin/cmake/release)

add_definitions(-DPluginFramework_CORE_LIBRARY)

macro(SEARCH_FILE list dir)
    file(GLOB_RECURSE children "${dir}/*.h" "${dir}/*.hpp" "${dir}/*.c" "${dir}/*.cpp")
    set(${list} ${children})
endmacro()
SEARCH_FILE(PLUGIN_RES_LIST ${PROJECT_CTK_DIR}/Libs/${PROJECT_NAME})

add_library(${PROJECT_NAME} ${PLUGIN_RES_LIST})
find_package(Qt5 REQUIRED COMPONENTS Core)
target_link_libraries(${PROJECT_NAME} Qt5::Core)
