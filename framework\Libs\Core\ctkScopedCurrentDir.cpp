/*=========================================================================

  Library:   CTK

  Copyright (c) Kitware Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=========================================================================*/

// Qt includes
#include <QDir>

// CTK includes
#include "ctkScopedCurrentDir.h"

class ctkScopedCurrentDirPrivate
{
public:
  ctkScopedCurrentDirPrivate();

  QString SavedCurrentPath;
};

// --------------------------------------------------------------------------
// ctkScopedCurrentDirPrivate methods

// --------------------------------------------------------------------------
ctkScopedCurrentDirPrivate::ctkScopedCurrentDirPrivate()
{
}

// --------------------------------------------------------------------------
// ctkScopedCurrentDir methods

// --------------------------------------------------------------------------
ctkScopedCurrentDir::ctkScopedCurrentDir(const QString& path) :
  d_ptr(new ctkScopedCurrentDirPrivate())
{
  Q_D(ctkScopedCurrentDir);
  d->SavedCurrentPath = QDir::currentPath();
  QDir::setCurrent(path);
}

// --------------------------------------------------------------------------
ctkScopedCurrentDir::~ctkScopedCurrentDir()
{
  Q_D(ctkScopedCurrentDir);
  QDir::setCurrent(d->SavedCurrentPath);
}

// --------------------------------------------------------------------------
QString ctkScopedCurrentDir::currentPath()const
{
  return QDir::currentPath();
}

// --------------------------------------------------------------------------
QString ctkScopedCurrentDir::savedCurrentPath()const
{
  Q_D(const ctkScopedCurrentDir);
  return d->SavedCurrentPath;
}
