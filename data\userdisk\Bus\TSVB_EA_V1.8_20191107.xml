<?xml version="1.0"?>
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="D:\TwinCAT\Io\EtherCAT\EtherCATInfo.xsd" Version="1.8">
	<Vendor>
		<Id>#x929</Id>
		<Name>TSV_Topstar</Name>
	</Vendor>
	<Descriptions>
		<Groups>
			<Group SortOrder="520">
				<Type>Drive</Type>
				<Name LcId="1033">TSVB-EA Servo Drives</Name>
				<Image16x14>DRIVE</Image16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x00000001" RevisionNo="#x00010008">TSV-HL</Type>
				<Name LcId="1033"><![CDATA[TSVB-EA EtherCAT(COE) Servo Drives Rev1.08_201901]]></Name>
				<Info>
					<StateMachine>
						<Behavior StartToSafeopNoSync="true"/>
					</StateMachine>
				</Info>
				<GroupType>Drive</GroupType>
				<Profile>
					<ChannelInfo>
						<ProfileNo>402</ProfileNo>
					</ChannelInfo>					
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>BOOLEAN</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(0)</Name>
								<BitSize>0</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>1040</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>1st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>2nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>3rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>4th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>5th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>6th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>7th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>8th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>9th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>10th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>11st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>12nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>13rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>14th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>15th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>16th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>17th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>18th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>19th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>20th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>21st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>22nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>23rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>24th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>25th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>784</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>26th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>816</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>27th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>848</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>28th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>880</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>29th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>912</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>30th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>944</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>31st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>976</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>32nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>1008</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>1040</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>1st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>2nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>3rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>4th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>5th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>6th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>7th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>8th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>9th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>10th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>11st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>12nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>13rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>14th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>15th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>16th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>17th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>18th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>19th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>20th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>21st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>22nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>23rd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>24th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>25th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>784</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>26th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>816</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>27th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>848</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>28th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>880</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>29th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>912</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>30th mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>944</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>31st mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>976</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>32nd mapping data</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>1008</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SyncManager0 communication type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SyncManager1 communication type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>SyncManager2 communication type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>SyncManager3 communication type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>40</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C10</Name>
								<BitSize>16</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Assign1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Assign2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Assign3</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Assign4</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>344</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync modes supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Command</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>RxPDO Toggle Failed</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Sync error</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
										<PdoMapping>RT</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>soft Min position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>soft Mux position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Encoder increments</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor revolutions</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6090</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Encoder increments per second</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor revolutions per second</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor revolutions</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Shaft revolutions</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Feed</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Shaft revolutions</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6093</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Numerator</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Feed constant</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Speed during search for switch</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Speed during search for zero</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60A4</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Profile jerk 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profile jerk 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Profile jerk 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Profile jerk 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Profile jerk 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Profile jerk 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C1</Name>
								<BitSize>342</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interp_data_1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interp_data_2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Interp_data_3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Interp_data_4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Interp_data_5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Interp_data_6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Interp_data_7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Interp_data_8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Interp_data_9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Interp_data_10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation time period value</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation time index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6510</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>User_motor_attr</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>encoder_type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>encoder_PPR_L</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>encoder_PPR_H</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>encoder_Zoffset</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>PolePairs</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>RateCurrent_x10A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>RateTorq_x10Nm</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>RateSpd</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>MaxSpd</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Jm</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Ke_volt_x100</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>ABS_offset_L</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>ABS_offset_H</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Rs</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>Ls</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010200</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Manufacturer device name</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Info>
									<DefaultString>TSVB-EA EtherCAT Servo Drives</DefaultString>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Manufacturer hardware version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100a</Index>
								<Name>Manufacturer software version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity object</Name>
								<Type>DT1018</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vendor ID</Name>
										<Info>
											<DefaultData>D1000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Product code</Name>
										<Info>
											<DefaultData>10000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Revision number</Name>
										<Info>
											<DefaultData>10000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial number</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO mapping</Name>
								<Type>DT1600</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>05</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>20007a60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>2000ff60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>08006060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>2nd receive PDO mapping</Name>
								<Type>DT1600</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>20007a60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>2000ff60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>3rd receive PDO mapping</Name>
								<Type>DT1600</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1603</Index>
								<Name>4th receive PDO mapping</Name>
								<Type>DT1600</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1a00</Index>
								<Name>1st transmit PDO mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>05</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>20006c60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>08006160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1a01</Index>
								<Name>2nd transmit PDO mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>20006c60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
                            <Object>
								<Index>#x1a02</Index>
								<Name>3rd transmit PDO mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
                            <Object>
								<Index>#x1a03</Index>
								<Name>4th transmit PDO mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>1040</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>1st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>2nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>3rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>4th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>5th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>6th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>7th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>8th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>9th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>10th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>11st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>12nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>13rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>14th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>15th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>16th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>17th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>18th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>19th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>20th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>21st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>22nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>23rd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>24th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>25th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>26th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>27th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>28th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>29th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>30th mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>31st mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>32nd mapping data</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c00</Index>
								<Name>Sync manager type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SyncManager0 communication type</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SyncManager1 communication type</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SyncManager2 communication type</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SyncManager3 communication type</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c12</Index>
								<Name>RxPDO assign</Name>
								<Type>DT1C12</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign1</Name>
										<Info>
											<DefaultData>0016</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign2</Name>
										<Info>
											<DefaultData>0116</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign3</Name>
										<Info>
											<DefaultData>0216</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign4</Name>
										<Info>
											<DefaultData>0316</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c13</Index>
								<Name>TxPDO assign</Name>
								<Type>DT1C12</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign1</Name>
										<Info>
											<DefaultData>001A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign2</Name>
										<Info>
											<DefaultData>011A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign3</Name>
										<Info>
											<DefaultData>021A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign4</Name>
										<Info>
											<DefaultData>031A</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>344</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>0e</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode</Name>
										<Info>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported</Name>
										<Info>
											<DefaultData>1F00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>24F40000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Command</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>30750000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPDO Toggle Failed</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>344</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>0e</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode</Name>
										<Info>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported</Name>
										<Info>
											<DefaultData>1F00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>24F40000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Command</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>30750000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPDO Toggle Failed</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2000</Index>
								<Name>para_code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3b01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2001</Index>
								<Name>motor_type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2002</Index>
								<Name>driver_type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2003</Index>
								<Name>led_init_status</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2004</Index>
								<Name>ctrl_mode</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>spd_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9600</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>spd_int_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>TorqueFilterCons</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2008</Index>
								<Name>spd_fb_flt_factor</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2009</Index>
								<Name>pst_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200A</Index>
								<Name>pst_ff_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200B</Index>
								<Name>pos_ff_FilterCons</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200C</Index>
								<Name>pst_cmd_num</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200D</Index>
								<Name>pst_cmd_den</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200E</Index>
								<Name>pos_cmd_mode</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x200F</Index>
								<Name>pst_cmd_sign</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2010</Index>
								<Name>pst_reachrange</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2011</Index>
								<Name>pst_toler_range</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1E00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2012</Index>
								<Name>ABS_encoder_usage</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2013</Index>
								<Name>pst_cmd_flt_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2014</Index>
								<Name>ccw_cw_en</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2015</Index>
								<Name>spd_jog_set</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2016</Index>
								<Name>spd_cmd_mode</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2017</Index>
								<Name>spd_max_lmt</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>C409</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2018</Index>
								<Name>pst_cmd_dir_flt_constant</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2019</Index>
								<Name>torque_cmd_source</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201A</Index>
								<Name>sin_freq_Hz</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>C002</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201B</Index>
								<Name>spd_cmd1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9CFF</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201C</Index>
								<Name>spd_reachrange</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201D</Index>
								<Name>Inertia_ratio</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201E</Index>
								<Name>current_ov_lmt_percent</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>C800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201F</Index>
								<Name>sft_ov_time_set</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1027</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2020</Index>
								<Name>ctr_mode_change_en</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2021</Index>
								<Name>Respond_Level</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2022</Index>
								<Name>in_ccw_torq_lmt_percent</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1801</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2023</Index>
								<Name>in_cw_torq_lmt_percent</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>E8FE</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2024</Index>
								<Name>spd_cmd_flt_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2025</Index>
								<Name>spd_ff_flt_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2026</Index>
								<Name>sr_jog_torq_lmt_percent</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2027</Index>
								<Name>STP_Baud</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2028</Index>
								<Name>acc_time_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2029</Index>
								<Name>dec_time_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202A</Index>
								<Name>Encoder_AB_flt_time</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202B</Index>
								<Name>Brake_contime_ms</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D007</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202C</Index>
								<Name>pulse_cmd_func</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202D</Index>
								<Name>ABS_encoder_BitNum</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202E</Index>
								<Name>spd_Integ_flt_constant</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x202F</Index>
								<Name>brake_svON_delay</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2030</Index>
								<Name>brake_close_delay</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2031</Index>
								<Name>brake_resp_spd</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2032</Index>
								<Name>brake_open_delay</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D007</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2033</Index>
								<Name>Brake_R_sel</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2034</Index>
								<Name>pos_ref_flt_constant</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2035</Index>
								<Name>IO_In_low_bit5_ON</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2036</Index>
								<Name>IO_In_high_bit5_ON</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2037</Index>
								<Name>IO_In_low_bit5_sign</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2038</Index>
								<Name>IO_In_high_bit5_sign</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2039</Index>
								<Name>IO_OUT_bit5_sign</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203A</Index>
								<Name>IO_filter_time</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203B</Index>
								<Name>Respond_rate</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203C</Index>
								<Name>TorqCMDFilterCons</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203D</Index>
								<Name>EEPROM_Mangement</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203E</Index>
								<Name>ALM_disable_set</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x203F</Index>
								<Name>TLObserv_flt_Hz</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2040</Index>
								<Name>torq_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9600</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2041</Index>
								<Name>torq_int_const</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2042</Index>
								<Name>spd_ff_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2043</Index>
								<Name>zhongli_comp_offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2044</Index>
								<Name>Kfr_pdff</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2045</Index>
								<Name>Brake_R_value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2046</Index>
								<Name>zd_yz_time</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>E803</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2047</Index>
								<Name>friction_ff_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2048</Index>
								<Name>pid_limit_index</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2049</Index>
								<Name>TLObserv_comp_gain</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204A</Index>
								<Name>current_set</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204B</Index>
								<Name>resv3</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204C</Index>
								<Name>TQ_spd_max</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D007</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204D</Index>
								<Name>resv4</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204E</Index>
								<Name>TQ_obj_pos</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x204F</Index>
								<Name>pos_tq_acc_time</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2050</Index>
								<Name>ABS_encoder_rst</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2051</Index>
								<Name>pos_cmd_PPR_L</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2052</Index>
								<Name>STP485_axis_num</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2054</Index>
								<Name>pos_cmd_PPR_H</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2060</Index>
								<Name>IO_input</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2061</Index>
								<Name>IO_output</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2062</Index>
								<Name>shouyao_pulse_value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x603f</Index>
								<Name>Error Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Control word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>m</Category>
									<PdoMapping>RT</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Status word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605a</Index>
								<Name>Quick stop option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605b</Index>
								<Name>Shutdown option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605c</Index>
								<Name>Disable operation option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605d</Index>
								<Name>Halt option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605e</Index>
								<Name>Fault reaction option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>08</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>RT</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of operation display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Position demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Internal position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Following error window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Following error time out</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Position window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Position window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Velocity sensor actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606b</Index>
								<Name>Velocity demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606c</Index>
								<Name>Velocity actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606d</Index>
								<Name> Velocity window</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606e</Index>
								<Name>Velocity window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606f</Index>
								<Name>Velocity threshold</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Velocity threshold Time</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target torque</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>RT</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6072</Index>
								<Name>Max torque</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinValue>#x0000</MinValue>
									<MaxValue>#x0FA0</MaxValue>
									<DefaultValue>#x0000</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>							
							<Object>
								<Index>#x6074</Index>
								<Name>Torque demand value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6075</Index>
								<Name>Motor rated current</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6076</Index>
								<Name>Motor rated torque</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6078</Index>
								<Name>Current actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6079</Index>
								<Name>DC link voltage value(mV)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607a</Index>
								<Name>Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>RT</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607b</Index>
								<Name>Position range limit</Name>
								<Type>DT607B</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min Position range limit</Name>
										<Info>
											<DefaultData>80000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Max Position range limit</Name>
										<Info>
											<DefaultData>7fffffff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607d</Index>
								<Name>Software position limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum position limit</Name>
										<Info>
											<DefaultData>00800000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Muximum position limit</Name>
										<Info>
											<DefaultData>ff7fffff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607f</Index>
								<Name>Max profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>C4090000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6080</Index>
								<Name>Max motor speed</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>C4090000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00006400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>End velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Profile acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>80969800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Profile deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>80969800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Motion profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Torque slope</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>32000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6088</Index>
								<Name>Torque profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x608f</Index>
								<Name>Encoder</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Encoder increments</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor revolutions</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor revolutions</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shaft revolutions</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Feed</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shaft revolutions</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Homing method</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>23</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Homing speeds</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed during search for switch</Name>
										<Info>
											<DefaultData>32000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed during search for zero</Name>
										<Info>
											<DefaultData>0A000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x609a</Index>
								<Name>Homing acceleration</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>aaaa0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60b0</Index>
								<Name>Profile offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60b1</Index>
								<Name>Velocity offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60b2</Index>
								<Name>Torque offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B8</Index>
								<Name>Touch probe function</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B9</Index>
								<Name>Touch probe status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BA</Index>
								<Name>Touch probe pos1 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BB</Index>
								<Name>Touch probe pos1 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BC</Index>
								<Name>Touch probe pos2 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BD</Index>
								<Name>Touch probe pos2 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60c0</Index>
								<Name>Interpolation sub mode select</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60c1</Index>
								<Name>Interpolation data record</Name>
								<Type>DT60C1</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interp_data_1</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interp_data_2</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interp_data_3</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interp_data_4</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60c2</Index>
								<Name>Interpolation time period</Name>
								<Type>DT60C2</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation time period value</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation time index</Name>
										<Info>
											<DefaultData>FD</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
							<Index>#x60f4</Index>
							<Name>Following error actualvalue</Name>
							<Type>DINT</Type>
							<BitSize>32</BitSize>
							<Info>
							  <DefaultData>00000000</DefaultData>
							</Info>
							<Flags>
							  <Access>ro</Access>
							  <Category>o</Category>
							  <PdoMapping>T</PdoMapping>
							</Flags>
							</Object>							
							<Object>
								<Index>#x60fc</Index>
								<Name>Position demand value inc</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60ff</Index>
								<Name>Target velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>RT</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6402</Index>
								<Name>Motor type 402</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6403</Index>
								<Name>Motor catalog number</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Info>
									<DefaultString>HL</DefaultString>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6404</Index>
								<Name>Motor manufacturer</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Info>
									<DefaultString>JTI</DefaultString>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6405</Index>
								<Name>Http motor catalog address</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Info>
									<DefaultString>http://www.whjti.com</DefaultString>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported drive mode</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>ED030000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6510</Index>
								<Name>Motor data</Name>
								<Type>DT6510</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>10</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>User_motor_attr</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>encoder_type</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>encoder_PPR_L</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>encoder_PPR_H</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>encoder_Zoffset</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PolePairs</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RateCurrent_x10A</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RateTorq_x10Nm</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RateSpd</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MaxSpd</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Jm</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ke_volt_x100</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABS_offset_L</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABS_offset_H</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Rs</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ls</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Sm MinSize="40" MaxSize="512" DefaultSize="384" StartAddress="#x1800" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="40" MaxSize="512" DefaultSize="384" StartAddress="#x1A00" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="10" StartAddress="#x1000" ControlByte="#x24" Enable="1">Outputs</Sm>
				<Sm DefaultSize="10" StartAddress="#x1100" ControlByte="#x00" Enable="1">Inputs</Sm>
				<Su>Drives</Su>
				<RxPdo Fixed="0">
					<Index>#x1600</Index>
					<Name>receive pdo0</Name>
					<Exclude>#x1601</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>torque demand  value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0" Sm="2">
					<Index>#x1601</Index>
					<Name>receive pdo1</Name>
					<Exclude>#x1600</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A00</Index>
					<Name>transmit pdo0</Name>
					<Exclude>#x1A01</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0" Sm="3">
					<Index>#x1A01</Index>
					<Name>transmit pdo1</Name>
					<Exclude>#x1A00</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<CoE DS402Channels="1" SdoInfo="1" PdoAssign="1" PdoConfig="1">
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
							<Comment>control mode</Comment>
						</InitCmd>
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x1C32</Index>
							<SubIndex>02</SubIndex>
							<Data>80841E00</Data>
							<Comment>Cycle time</Comment>
						</InitCmd>
					</CoE>
				</Mailbox>
				<Dc Unknown64Bit="1" UnknownFRMW="1">
					<OpMode>
						<Name>DcSync</Name>
						<Desc>DC for synchronization</Desc>
						<AssignActivate>#x0300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
					</OpMode>
					<OpMode>
						<Name>DcOff</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>080C66003200</ConfigData>
				</Eeprom>
				<Image16x14>DRIVE</Image16x14>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>
