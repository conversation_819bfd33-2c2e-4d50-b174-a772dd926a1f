﻿#include <ctkPluginContext.h>
#include <ctkPluginFramework.h>
#include <ctkPluginException.h>
#include <ctkPluginFrameworkFactory.h>
#include <ctkPluginFrameworkLauncher.h>

#include <QDebug>
#include <QDirIterator>
#include <QApplication>
#include <QCoreApplication>
#include <QTextCodec>
#include <QProcess>
#include <QTimer>


int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // TODO 子进程启动流程
    // 获取参数 --subprocess 插件名
    // framework 可以直接启动插件 如 framework.start("com.mltor.ainc.detached-process")
    // 不会启动所有的插件，但需要启动插件所依赖的所有插件，但实际上独立进程不会依赖其它插件


    //启动框架
    QString path = QCoreApplication::applicationDirPath() + "/plugins";
    ctkPluginFrameworkLauncher::addSearchPath(path,true);
    ctkProperties properties;
    properties.insert(ctkPluginConstants::FRAMEWORK_STORAGE_CLEAN, ctkPluginConstants::FRAMEWORK_STORAGE_CLEAN_ONFIRSTINIT);
    ctkPluginFrameworkFactory fwFactory(properties);
    QSharedPointer<ctkPluginFramework> framework = fwFactory.getFramework();
    try {
        framework->init();
        framework->start();
    } catch (const ctkPluginException& exc) {
        qCritical() << QLatin1String("插件框架启动失败。") << exc;
        return -1;
    }

    //安装插件
    //获取插件上下文
    ctkPluginContext* pluginContext = framework->getPluginContext();
    QStringList libFilter;

    //安装插件
    //平台判断
#if defined(Q_OS_WIN)
    libFilter << "*.dll";
#elif defined(Q_OS_LINUX)
    libFilter << "*.so";
#elif defined(Q_OS_MAC)
    libFilter << "*.dylib";
#endif
    QDirIterator dirIter(path, libFilter, QDir::Files);
    QString fileLocation;
    while(dirIter.hasNext()) {
        try {
            fileLocation = dirIter.next();
            qDebug() << "1.安装插件：" << fileLocation;
            pluginContext->installPlugin(QUrl::fromLocalFile(fileLocation));
        }
        catch (const ctkPluginException& exc) {
            qCritical() << Q_FUNC_INFO << fileLocation << exc.what() << exc.cause() << exc.message() << exc.stackTrace();
            return -1;
        }
    }

    qDebug() << argv[0] << argv[1];
    if (argc > 1 && QString(argv[1]) == "-subprocess") {
        qDebug() << "========================================================";
        // TODO 标记进程名，需要区分

        // char* new_name = "ainc:pluginadmin";
        // smaug_os_argv = argv;
        // if (smaug_init_setproctitle() == SMAUG_PROCTITLE_OK) {
        //     smaug_setproctitle(new_name);
        // }

        ctkPluginFrameworkLauncher::start("pluginadmin", ctkPlugin::START_ACTIVATION_POLICY,pluginContext);
    } else {

    //启动插件
    foreach (const QSharedPointer<ctkPlugin> &plugin, pluginContext->getPlugins()) {
        try {
            QHash<QString, QString> headers = plugin->getHeaders();
            qDebug() << "2.启动插件，清单信息:" << headers;
            
            if(headers.value("Plugin-SymbolicName") != "com.mltor.ainc.hmi") {
                plugin->start();
            }
        }
        catch (const ctkPluginException& exc) {
            qCritical() << exc.what();
            return -1;
        }
    }

    // QTimer::singleShot(10000, [=]{
    //     QProcess::execute(qApp->applicationFilePath(), {"-subprocess"});
    // });
}


    return a.exec();
}
