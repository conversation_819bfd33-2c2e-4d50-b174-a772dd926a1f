/*=============================================================================

  Library: CTK

  Copyright (c) German Cancer Research Center,
    Division of Medical and Biological Informatics

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=============================================================================*/

#include "ctkApplicationDescriptor.h"

#include <ctkPluginConstants.h>

const QString ctkApplicationDescriptor::APPLICATION_NAME = "application.name";
const QString ctkApplicationDescriptor::APPLICATION_ICON = "application.icon";
const QString ctkApplicationDescriptor::APPLICATION_PID = "service.pid"; //ctkPluginConstants::SERVICE_PID;
const QString ctkApplicationDescriptor::APPLICATION_VERSION = "application.version";
const QString ctkApplicationDescriptor::APPLICATION_VENDOR = "service.vendor"; //ctkPluginConstants::SERVICE_VENDOR;
const QString ctkApplicationDescriptor::APPLICATION_VISIBLE = "application.visible";
const QString ctkApplicationDescriptor::APPLICATION_LAUNCHABLE = "application.launchable";
const QString ctkApplicationDescriptor::APPLICATION_LOCKED = "application.locked";
const QString ctkApplicationDescriptor::APPLICATION_DESCRIPTION = "application.description";
const QString ctkApplicationDescriptor::APPLICATION_DOCUMENTATION = "application.documentation";
const QString ctkApplicationDescriptor::APPLICATION_COPYRIGHT = "application.copyright";
const QString ctkApplicationDescriptor::APPLICATION_LICENSE = "application.license";
const QString ctkApplicationDescriptor::APPLICATION_CONTAINER = "application.container";
const QString ctkApplicationDescriptor::APPLICATION_LOCATION = "application.location";
