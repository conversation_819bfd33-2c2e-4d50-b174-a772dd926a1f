<?xml version="1.0"?>
<!-- edited with XMLSpy v2008 rel. 2 (http://www.altova.com) by <PERSON><PERSON> (Elmo Motion Control) -->
<!--ElmoSubVer 11-->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.4">
	<Vendor>
		<Id>#x0000009A</Id>
		<Name>Elmo Motion Control</Name>
		<ImageData16x14>424DE6000000000000007600000028000000100000000E000000010004000000000070000000C40E0000C40E000000000000000000000000000000008000008000000080800080000000800080008080000080808000C0C0C0000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF79999997FFFFFFFF99977999FFFFFFFF9998FF89FFFFFFFF99999FFFFFFFFFFF999997FFFF777777999999FFFF777777799999FFFF877777FFFFFFFFFFF77777FFFFFFFF78ff8777FFFFFFFF77777777FFFFFFFF77777777FFFFFFFFFFFFFFFF</ImageData16x14>
	</Vendor>
	<Descriptions>
		<Groups>
			<Group SortOrder="520">
				<Type>SimplIQ_GOLD</Type>
				<Name LcId="1033">Elmo Motion Control Gold EtherCAT Family Devices</Name>
				<ImageData16x14>424DE6000000000000007600000028000000100000000E000000010004000000000070000000C40E0000C40E000000000000000000000000000000008000008000000080800080000000800080008080000080808000C0C0C0000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFFFF00FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF79999997FFFFFFFF99977999FFFFFFFF9998FF89FFFFFFFF99999FFFFFFFFFFF999997FFFF777777999999FFFF777777799999FFFF877777FFFFFFFFFFF77777FFFFFFFF78ff8777FFFFFFFF77777777FFFFFFFF77777777FFFFFFFFFFFFFFFF</ImageData16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x00030924" RevisionNo="#x00000001">Elmo Drive </Type>
				<Name LcId="1033"><![CDATA[Gold EtherCAT GCON Boot Rev:0x00000001]]></Name>
				<GroupType>SimplIQ_GOLD</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>INT24</Name>
								<BitSize>24</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(0)</Name>
								<BitSize>0</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(8)</Name>
								<BitSize>64</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(19)</Name>
								<BitSize>152</BitSize>
							</DataType>
							<DataType>
								<Name>DT1400</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>COB-ID used by PDO1 RX</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Transmission Type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Inhibit time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>CMS priority group</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Controlword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1800</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>COB-ID for PDO1 TX</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Transmission Type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Inhibit Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>CMS priority group</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>statusword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FEARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT60FEARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1400</Index>
								<Name>1st receive PDO-Parameter</Name>
								<Type>DT1400</Type>
								<BitSize>96</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Transmission Type</Name>
										<Info>
											<DefaultData>ff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT1600</Type>
								<BitSize>96</BitSize>
							</Object>
							<Object>
								<Index>#x1800</Index>
								<Name>1st transmit PDO-Parameter</Name>
								<Type>DT1800</Type>
								<BitSize>96</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Transmission Type</Name>
										<Info>
											<DefaultData>ff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st transmit PDO-Mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>96</BitSize>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Controlword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>m</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Statusword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1800" ControlByte="#x26" Enable="1" Watchdog="false">MBoxOut</Sm>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1900" ControlByte="#x22" Enable="1" Watchdog="false">MBoxIn</Sm>
				<Sm MinSize="0" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1100" ControlByte="#x64" Enable="1" Watchdog="true">Outputs</Sm>
				<Sm MinSize="0" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1180" ControlByte="#x20" Enable="1" Watchdog="false">Inputs</Sm>
				<Su>Drive</Su>
				<RxPdo Fixed="1" Sm="2">
					<Index>#x1600</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index>#x1A00</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<EoE/>
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true" CompleteAccess="false"/>
					<FoE/>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC Sync</Name>
						<Desc>DC for synchronization</Desc>
						<AssignActivate>#x0300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
					</OpMode>
					<OpMode>
						<Name>DC Off</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050C0304E803</ConfigData>
					<BootStrap>00188C0000198C00</BootStrap>
					<Category>
						<CatNo>30</CatNo>
						<Data>00000000002F0101000001000000000011110000000000000000000000000000</Data>
					</Category>
					<Category>
						<CatNo>41</CatNo>
						<Data>000018800026000101</Data>
					</Category>
				</Eeprom>
				<Image16x14>DRIVE</Image16x14>
			</Device>
			<Device Physics="YY">
				<Type ProductCode="#x00030925" RevisionNo="#x00000001">Elmo Drive </Type>
				<Name LcId="1033"><![CDATA[Gold EtherCAT GCON Boot Rev:0x00000001]]></Name>
				<GroupType>SimplIQ_GOLD</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>INT24</Name>
								<BitSize>24</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(0)</Name>
								<BitSize>0</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(8)</Name>
								<BitSize>64</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(19)</Name>
								<BitSize>152</BitSize>
							</DataType>
							<DataType>
								<Name>DT1400</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>COB-ID used by PDO1 RX</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Transmission Type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Inhibit time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>CMS priority group</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Controlword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1800</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>COB-ID for PDO1 TX</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Transmission Type</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Inhibit Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>CMS priority group</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>96</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>statusword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FEARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT60FEARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1400</Index>
								<Name>1st receive PDO-Parameter</Name>
								<Type>DT1400</Type>
								<BitSize>96</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Transmission Type</Name>
										<Info>
											<DefaultData>ff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT1600</Type>
								<BitSize>96</BitSize>
							</Object>
							<Object>
								<Index>#x1800</Index>
								<Name>1st transmit PDO-Parameter</Name>
								<Type>DT1800</Type>
								<BitSize>96</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Transmission Type</Name>
										<Info>
											<DefaultData>ff</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st transmit PDO-Mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>96</BitSize>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Controlword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>m</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Statusword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1800" ControlByte="#x26" Enable="1" Watchdog="false">MBoxOut</Sm>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1900" ControlByte="#x22" Enable="1" Watchdog="false">MBoxIn</Sm>
				<Sm MinSize="0" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1100" ControlByte="#x64" Enable="1" Watchdog="true">Outputs</Sm>
				<Sm MinSize="0" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1180" ControlByte="#x20" Enable="1" Watchdog="false">Inputs</Sm>
				<Su>Drive</Su>
				<RxPdo Fixed="1" Sm="2">
					<Index>#x1600</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index>#x1A00</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<EoE/>
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true" CompleteAccess="true"/>
					<FoE/>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC Sync</Name>
						<Desc>DC for synchronization</Desc>
						<AssignActivate>#x0300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
					</OpMode>
					<OpMode>
						<Name>DC Off</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050C0304E803</ConfigData>
					<BootStrap>00188C0000198C00</BootStrap>
					<Category>
						<CatNo>30</CatNo>
						<Data>00000000002F0101000001000000000011110000000000000000000000000000</Data>
					</Category>
					<Category>
						<CatNo>41</CatNo>
						<Data>000018800026000101</Data>
					</Category>
				</Eeprom>
				<Image16x14>DRIVE</Image16x14>
			</Device>
			<Device Physics="YY">
				<Type ProductCode="#x00030924" RevisionNo="#x00010420">Elmo Drive </Type>
				<Name LcId="1033"><![CDATA[Gold EtherCAT GCON Drive Rev:0x00010420]]></Name>
				<Info>
					<IdentificationReg134>false</IdentificationReg134>
				</Info>
				<GroupType>SimplIQ_GOLD</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>INT24</Name>
								<BitSize>24</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>REAL</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(0)</Name>
								<BitSize>0</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(8)</Name>
								<BitSize>64</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(19)</Name>
								<BitSize>152</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(64)</Name>
								<BitSize>512</BitSize>
							</DataType>
							<DataType>
								<Name>DT1003ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>512</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>16</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1003</Name>
								<BitSize>528</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1003ARR</Type>
									<BitSize>512</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1010ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1010</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1010ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1011ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1011</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1011ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor Id</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product Code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision Number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial Number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT10E0</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Configured Alias Reg</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Write Configured Alias Persistent</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT10F1</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Highest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Local Error Reaction</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Sync error counter limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1111</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>IP Address</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Subnet Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Default Gateway</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>DNS Server</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>DNS Name</Name>
									<Type>STRING(8)</Type>
									<BitSize>64</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1601</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1602</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1603</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Velocity Offset</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1604</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Max.Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1605</Name>
								<BitSize>240</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Target Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Max.Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1606</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Target Velocity</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Velocity Offset</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Torque Offset</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Controlword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1607ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1607</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1607ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1608ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1608</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1608ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A01</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A02</Name>
								<BitSize>176</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A03</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Velocity Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A04</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Following Position Error</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A07ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A07</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A07ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A08ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A08</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A08ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of errors</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C00ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C10</Name>
								<BitSize>16</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SM0 PDO Assigment</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C11</Name>
								<BitSize>16</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SM1 PDO Assigment</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>480</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>30</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>496</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C12ARR</Type>
									<BitSize>480</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C13ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>560</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>35</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C13</Name>
								<BitSize>576</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C13ARR</Type>
									<BitSize>560</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>608</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Minimum delay time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle exceeded counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>SubIndex 00E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>SubIndex 00F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>SubIndex 010</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>SubIndex 011</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>SubIndex 012</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>SubIndex 013</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>SubIndex 014</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>SubIndex 015</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>SubIndex 016</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>SubIndex 017</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>SubIndex 018</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>SubIndex 019</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>SubIndex 01A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>SubIndex 01B</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>SubIndex 01C</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>SubIndex 01D</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>SubIndex 01E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>SubIndex 01F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C33</Name>
								<BitSize>608</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Min delay time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle exceeded counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>SubIndex 00E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>SubIndex 00F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>SubIndex 010</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>SubIndex 011</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>SubIndex 012</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>SubIndex 013</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>SubIndex 014</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>SubIndex 015</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>SubIndex 016</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>SubIndex 017</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>SubIndex 018</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>SubIndex 019</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>SubIndex 01A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>SubIndex 01B</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>SubIndex 01C</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>SubIndex 01D</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>SubIndex 01E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>SubIndex 01F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2020</Name>
								<BitSize>160</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Torque limit</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Time limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Distance limit in counts</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Detection Velocity Limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Detection Velocity Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT207B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Highest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min auxiliary  position range limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max auxiliary  position range limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2081</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Largest sub index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Serial Encoder Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profiler Data Base Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>DL Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>SDO ELMO Format Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>MO Failed to start Reason</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>ECAM error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT20B0</Name>
								<BitSize>304</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Main position feedback socket (CA[45])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Speed feedback socket (CA[46])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Commutation socket (CA[47])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Position reference socket (CA[68])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Speed reference socket (CA[69])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Current reference socket (CA[70])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Touch-probe socket (CA[87])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Homing 402 - capture socket (OV[54])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Additional position socket (CA[79])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT20FC</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Clear Absolute Multi Pos</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>wo</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Reset EnDAT Error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>wo</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2110ARR</Name>
								<BaseType>REAL</BaseType>
								<BitSize>2016</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>63</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT2110</Name>
								<BitSize>2032</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT2110ARR</Type>
									<BitSize>2016</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2202</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Extended Inputs Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Extended Inputs Logic</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Extended Inputs Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2205</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Analog input 1</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Analog input 2</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT22A1</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Extended Outputs Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Extended Outputs Logic</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Extended Outputs Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT22A3</Name>
								<BitSize>64</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Drive Temperature [C]</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Drive Temperature [F]</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Sensor Temperature</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F00</Name>
								<BitSize>784</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>UI[1]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>UI[2]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>UI[3]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>UI[4]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>UI[5]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>UI[6]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>UI[7]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>UI[8]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>UI[9]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>UI[10]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>UI[11]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>UI[12]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>UI[13]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>UI[14]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>UI[15]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>UI[16]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>UI[17]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>UI[18]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>UI[19]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>UI[20]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>UI[21]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>UI[22]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>UI[23]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>UI[24]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F01</Name>
								<BitSize>784</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>UF[1]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>UF[2]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>UF[3]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>UF[4]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>UF[5]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>UF[6]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>UF[7]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>UF[8]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>UF[9]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>UF[10]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>UF[11]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>UF[12]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>UF[13]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>UF[14]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>UF[15]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>UF[16]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>UF[17]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>UF[18]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>UF[19]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>UF[20]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>UF[21]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>UF[22]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>UF[23]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>UF[24]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F45</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Low voltage level [mV]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>High voltage level [mV]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Analog sensors min amplitude [A2D^2]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>High Temperature sensing [C]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT604AARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT604A</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT604AARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607BARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT607B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT607BARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607DARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT607DARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608FARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT608FARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6090ARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6090</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6090ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6091ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6092ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6096</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Numerator</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Divisor</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6097</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Numerator</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Divisor</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6099ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation time period value</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation time index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60E3ARR</Name>
								<BaseType>SINT</BaseType>
								<BitSize>288</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>36</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT60E3</Name>
								<BitSize>304</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT60E3ARR</Type>
									<BitSize>288</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Physical outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Bit mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device Type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010200</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error Register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1002</Index>
								<Name>Manufacturer Status Register</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x1003</Index>
								<Name>Pre-defined Error Field</Name>
								<Type>DT1003</Type>
								<BitSize>528</BitSize>
								<Info>
									<SubItem>
										<Name>Number of elements</Name>
										<Info>
											<DefaultData>10</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 005</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 006</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 007</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 008</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 009</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Manufacturer Device Name</Name>
								<Type>STRING(19)</Type>
								<BitSize>152</BitSize>
								<Info>
									<DefaultData>456C6D6F204D6F74696F6E20436F6E74726F6C</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Manufacture Hardware Version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100A</Index>
								<Name>Manufacturer Software Version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100B</Index>
								<Name>Bootloader Version</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1010</Index>
								<Name>Store Parameters</Name>
								<Type>DT1010</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x1011</Index>
								<Name>Restore Parameters</Name>
								<Type>DT1011</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity Object</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x10E0</Index>
								<Name>Device ID Reload</Name>
								<Type>DT10E0</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x10F1</Index>
								<Name>Sync error setting</Name>
								<Type>DT10F1</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Highest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Local Error Reaction</Name>
										<Info>
											<DefaultValue>#x00000002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error counter limit</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT1600</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>2nd receive PDO-Mapping</Name>
								<Type>DT1601</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>3rd receive PDO-Mapping</Name>
								<Type>DT1602</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1603</Index>
								<Name>4th receive PDO-Mapping</Name>
								<Type>DT1603</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1604</Index>
								<Name>5th receive PDO-Mapping</Name>
								<Type>DT1604</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1605</Index>
								<Name>6th receive PDO-Mapping</Name>
								<Type>DT1605</Type>
								<BitSize>240</BitSize>
							</Object>
							<Object>
								<Index>#x1606</Index>
								<Name>7th receive PDO-Mapping</Name>
								<Type>DT1606</Type>
								<BitSize>208</BitSize>
							</Object>
							<Object>
								<Index>#x1607</Index>
								<Name>8th receive PDO-Mapping</Name>
								<Type>DT1607</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1608</Index>
								<Name>9th receive PDO-Mapping</Name>
								<Type>DT1608</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st transmit PDO-Mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1A01</Index>
								<Name>2nd transmit PDO-Mapping</Name>
								<Type>DT1A01</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1A02</Index>
								<Name>3rd transmit PDO-Mapping</Name>
								<Type>DT1A02</Type>
								<BitSize>176</BitSize>
							</Object>
							<Object>
								<Index>#x1A03</Index>
								<Name>4th transmit PDO-Mapping</Name>
								<Type>DT1A03</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1A04</Index>
								<Name>5th transmit PDO-Mapping</Name>
								<Type>DT1A04</Type>
								<BitSize>208</BitSize>
							</Object>
							<Object>
								<Index>#x1A07</Index>
								<Name>8th transmit PDO-Mapping</Name>
								<Type>DT1A07</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1A08</Index>
								<Name>9th transmit PDO-Mapping</Name>
								<Type>DT1A08</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1C00</Index>
								<Name>SM Communication Type</Name>
								<Type>DT1C00</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1C10</Index>
								<Name>Sync Manager 0 Communication Type</Name>
								<Type>DT1C10</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C11</Index>
								<Name>Sync Manager 1 Communication Type</Name>
								<Type>DT1C11</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C12</Index>
								<Name>Sync Manager 2 Communication Type</Name>
								<Type>DT1C12</Type>
								<BitSize>496</BitSize>
							</Object>
							<Object>
								<Index>#x1C13</Index>
								<Name>Sync Manager 3 Communication Type</Name>
								<Type>DT1C13</Type>
								<BitSize>576</BitSize>
							</Object>
							<Object>
								<Index>#x1C32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>608</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultValue>#x20</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Type</Name>
										<Info>
											<DefaultValue>#x0002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultValue>#x000F4240</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultValue>#x00000000</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync types supported</Name>
										<Info>
											<DefaultValue>#x0007</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultValue>#x0003D090</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum delay time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle exceeded counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 017</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 018</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 019</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01A</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01B</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01C</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01D</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>608</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultValue>#x20</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Type</Name>
										<Info>
											<DefaultValue>#x0002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultValue>#x000F4240</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync types supported</Name>
										<Info>
											<DefaultValue>#x0007</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultValue>#x0003D090</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min delay time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle exceeded counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 017</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 018</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 019</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01A</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01B</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01C</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01D</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>Fast Reference</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x200A</Index>
								<Name>Manufacture Boot Version</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201B</Index>
								<Name>Filtered RMS Current</Name>
								<Type>REAL</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2020</Index>
								<Name>Homing on block limits</Name>
								<Type>DT2020</Type>
								<BitSize>160</BitSize>
							</Object>
							<Object>
								<Index>#x2041</Index>
								<Name>Timestamp</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2046</Index>
								<Name>DC clock inhibit time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2060</Index>
								<Name>Parameters Checksum</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2061</Index>
								<Name>FoE Download Parameters Error</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2062</Index>
								<Name>FoE Parameters Last String Send To Drive</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x207B</Index>
								<Name>YM[] Auxiliary Position range limit</Name>
								<Type>DT207B</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x2081</Index>
								<Name>EE[] Extendend Error</Name>
								<Type>DT2081</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>Largest sub index supported</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial Encoder Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profiler Data Base Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DL Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SDO ELMO Format Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MO Failed to start Reason</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ECAM error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2084</Index>
								<Name>Serial Sensor Status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2085</Index>
								<Name>Extra Status Reg</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x2086</Index>
								<Name>STO Status Reg</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2087</Index>
								<Name>PAL Version</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x20A0</Index>
								<Name>Additional position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x20B0</Index>
								<Name>Socket Additional Functions</Name>
								<Type>DT20B0</Type>
								<BitSize>304</BitSize>
							</Object>
							<Object>
								<Index>#x20E0</Index>
								<Name>Ecat alias object</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x20FC</Index>
								<Name>Absolute Sensors Functions</Name>
								<Type>DT20FC</Type>
								<BitSize>48</BitSize>
								<Info/>
							</Object>
							<Object>
								<Index>#x20FD</Index>
								<Name>Clear Digital Inputs</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2110</Index>
								<Name>KI for inner loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2111</Index>
								<Name>KP for inner loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2112</Index>
								<Name>KP for outer loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2113</Index>
								<Name>Vel Filt1 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2114</Index>
								<Name>Vel Filt1 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2115</Index>
								<Name>Vel Filt1 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2116</Index>
								<Name>Vel Filt1 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2117</Index>
								<Name>Vel Filt2 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2118</Index>
								<Name>Vel Filt2 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2119</Index>
								<Name>Vel Filt2 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211A</Index>
								<Name>Vel Filt2 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211B</Index>
								<Name>Pos Filt2 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211C</Index>
								<Name>Pos Filt2 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211D</Index>
								<Name>Pos Filt2 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211E</Index>
								<Name>Pos Filt2 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2202</Index>
								<Name>Extended Inputs</Name>
								<Type>DT2202</Type>
								<BitSize>112</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Value</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Logic</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Mask</Name>
										<Info>
											<DefaultData>FFFFFFFF</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2203</Index>
								<Name>App Object</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x2205</Index>
								<Name>Analog Input</Name>
								<Type>DT2205</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x2206</Index>
								<Name>Digital Supply In mV</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x22A1</Index>
								<Name>Extended Outputs</Name>
								<Type>DT22A1</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x22A3</Index>
								<Name>Temperature Array</Name>
								<Type>DT22A3</Type>
								<BitSize>64</BitSize>
							</Object>
							<Object>
								<Index>#x22A4</Index>
								<Name>Motor temperature</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E00</Index>
								<Name>Gain scheduling manual index</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E10</Index>
								<Name>Set Position On TouchProbe</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E15</Index>
								<Name>Gantry Yaw Offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F00</Index>
								<Name>UI[] Commands</Name>
								<Type>DT2F00</Type>
								<BitSize>784</BitSize>
							</Object>
							<Object>
								<Index>#x2F01</Index>
								<Name>UF[] Commands</Name>
								<Type>DT2F01</Type>
								<BitSize>784</BitSize>
							</Object>
							<Object>
								<Index>#x2F05</Index>
								<Name>Hardware Type WS[8]</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F41</Index>
								<Name>Configuration Object</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F45</Index>
								<Name>ET[] Extra Threshold</Name>
								<Type>DT2F45</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Low voltage level [mV]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>High voltage level [mV]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Analog sensors min amplitude [A2D^2]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>High Temperature sensing [C]</Name>
										<Info>
											<DefaultData>5500</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2F75</Index>
								<Name>Interpolation cycle timeout</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>1</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6007</Index>
								<Name>Abort connection code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Error Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Controlword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>m</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Statusword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605A</Index>
								<Name>Quick Stop option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>2</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Shutdown option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Disable operation option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605D</Index>
								<Name>Halt option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Fault reaction option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of operation display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Position demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Position actual internal value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Position following error window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Following error time out</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Position window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Position window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Velocity sensor actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606A</Index>
								<Name>Velocity sensor selection code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606B</Index>
								<Name>Velocity demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Velocity actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606D</Index>
								<Name>Velocity window</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606E</Index>
								<Name>Velocity window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606F</Index>
								<Name>Velocity threshold</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Velocity threshold time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target torque</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6072</Index>
								<Name>Maximal torque</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6073</Index>
								<Name>Maximal Current</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6074</Index>
								<Name>Torque demand</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6075</Index>
								<Name>Motor rated current</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6076</Index>
								<Name>Motor rated torque</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6078</Index>
								<Name>Current actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6079</Index>
								<Name>DC link voltage</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607B</Index>
								<Name>Position range limit</Name>
								<Type>DT607B</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x607C</Index>
								<Name>Home offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Software position limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x607E</Index>
								<Name>Polarity</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607F</Index>
								<Name>Max profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6080</Index>
								<Name>Max motor speed</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>End velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Profile acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Profile deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Quick stop deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Motion profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Torque slope</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x608F</Index>
								<Name>Position encoder resolution</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6090</Index>
								<Name>Velocity encoder resolution</Name>
								<Type>DT6090</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed constant</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6096</Index>
								<Name>Velocity factor</Name>
								<Type>DT6096</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6097</Index>
								<Name>Acceleration factor</Name>
								<Type>DT6097</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Homing method</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Homing speeds</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x609A</Index>
								<Name>Homing acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B0</Index>
								<Name>Position offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B1</Index>
								<Name>Velocity offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B2</Index>
								<Name>Torque offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B8</Index>
								<Name>Touch probe function</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B9</Index>
								<Name>Touch probe status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BA</Index>
								<Name>Touch probe pos1 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BB</Index>
								<Name>Touch probe pos1 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BC</Index>
								<Name>Touch probe pos2 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BD</Index>
								<Name>Touch probe pos2 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C2</Index>
								<Name>Interpolation time period</Name>
								<Type>DT60C2</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x60C5</Index>
								<Name>Max acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C6</Index>
								<Name>Max deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E3</Index>
								<Name>Supported Homing Method</Name>
								<Type>DT60E3</Type>
								<BitSize>304</BitSize>
							</Object>
							<Object>
								<Index>#x60F2</Index>
								<Name>Positioning option code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F4</Index>
								<Name>Following error actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FA</Index>
								<Name>Control effort</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FC</Index>
								<Name>Position demand internal value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Digital inputs</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Target velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported drive modes</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1800" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1900" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm MinSize="0" MaxSize="#x20" DefaultSize="#x20" StartAddress="#x1100" ControlByte="#x64" Enable="1">Outputs</Sm>
				<Sm MinSize="0" MaxSize="#x20" DefaultSize="#x20" StartAddress="#x1180" ControlByte="#x20" Enable="1">Inputs</Sm>
				<Su>Drive</Su>
				<RxPdo Fixed="1" Sm="2">
					<Index>#x1600</Index>
					<Name>Outputs</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1601</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1602</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1603</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1604</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1605</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1606</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Offset</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1607</Index>
					<Name>Outputs</Name>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1608</Index>
					<Name>Outputs</Name>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160A</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160B</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160C</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160D</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160E</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6073</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Current</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160F</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1611</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6081</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Velocity</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1612</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6082</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>End velocity</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1613</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6083</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Acceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1614</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6084</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Deceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1615</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6087</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Torque Slope</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1616</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1617</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1618</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Offset</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1619</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B8</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch Probe Function</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161A</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x2E00</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Gain scheduling manual index</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161C</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161D</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161E</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607E</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Polarity</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161F</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6085</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Quick stop deceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1620</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x22A1</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Extended Outputs </Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1621</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x2005</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Fast Reference</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index>#x1A00</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A01</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606B</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6074</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque demand value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A02</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A03</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A04</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60F4</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Following error actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A07</Index>
					<Name>Inputs</Name>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A08</Index>
					<Name>Inputs</Name>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0A</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0B</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0C</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6062</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Demand [UU]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0D</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6063</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Actual position [counts]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0E</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0F</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6069</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity sensor actual value [counts/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A10</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x606B</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand [cnt/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A11</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A12</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6074</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque demand value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A13</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A14</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60B9</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch Probe status</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A15</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BA</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch Probe Pos1 Positive</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A16</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BB</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch Probe Pos1 Negative</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A17</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BC</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch probe pos2 pos value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A18</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6079</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>DC link circuit voltage</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A19</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60F4</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Following error</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1A</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FA</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Control Effort [cnt/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1B</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FC</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Demand Value [cnt]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1C</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1D</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2205</Index>
						<SubIndex>1</SubIndex>
						<BitLen>16</BitLen>
						<Name>Analog Input 1</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1E</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x20A0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Auxiliary position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1F</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6078</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Current actual value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A20</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch probe pos2 neg value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A21</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2085</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Extra Status Reg</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A22</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x1002</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Elmo Status Reg</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A23</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2202</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Extended Inputs Value</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A24</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2203</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>App Object</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A26</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x603F</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Error Code</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<EoE/>
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true" CompleteAccess="false">
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
							<Comment>Op mode</Comment>
						</InitCmd>
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x60C2</Index>
							<SubIndex>1</SubIndex>
							<Data>02</Data>
							<Comment>Cycle time</Comment>
						</InitCmd>
					</CoE>
					<FoE/>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC Sync</Name>
						<Desc>DC for synchronization</Desc>
						<AssignActivate>#x0300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
					</OpMode>
					<OpMode>
						<Name>DC Off</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<ESC>
					<Reg0108>#x0</Reg0108>
					<Reg0400>#x9C2</Reg0400>
					<Reg0410>#x03E8</Reg0410>
					<Reg0420>#x2710</Reg0420>
				</ESC>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050C0304E803</ConfigData>
					<BootStrap>00188C0000198C00</BootStrap>
					<Category>
						<CatNo>30</CatNo>
						<Data>00000000002F0101000001000000000011110000000000000000000000000000</Data>
					</Category>
					<Category>
						<CatNo>41</CatNo>
						<Data>000018800026000101</Data>
					</Category>
				</Eeprom>
			</Device>
			<Device Physics="YY">
				<Type ProductCode="#x00030925" RevisionNo="#x00010420">Elmo Drive </Type>
				<Name LcId="1033"><![CDATA[Gold EtherCAT GCON Drive ID Selector Rev:0x00010420]]></Name>
				<Info>
					<IdentificationReg134>true</IdentificationReg134>
				</Info>
				<GroupType>SimplIQ_GOLD</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>INT24</Name>
								<BitSize>24</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>REAL</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(0)</Name>
								<BitSize>0</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(8)</Name>
								<BitSize>64</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(19)</Name>
								<BitSize>152</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(64)</Name>
								<BitSize>512</BitSize>
							</DataType>
							<DataType>
								<Name>DT1003ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>512</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>16</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1003</Name>
								<BitSize>528</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1003ARR</Type>
									<BitSize>512</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1010ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1010</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1010ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1011ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1011</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1011ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor Id</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product Code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision Number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial Number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT10E0</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Configured Alias Reg</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Write Configured Alias Persistent</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT10F1</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Highest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Local Error Reaction</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Sync error counter limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1111</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>IP Address</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Subnet Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Default Gateway</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>DNS Server</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>DNS Name</Name>
									<Type>STRING(8)</Type>
									<BitSize>64</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1601</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1602</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1603</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Velocity Offset</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1604</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Max.Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1605</Name>
								<BitSize>240</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Target Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Max.Torque</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Controlword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1606</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Target Position</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Outputs</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Target Velocity</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Velocity Offset</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Torque Offset</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Controlword</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1607ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1607</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1607ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1608ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1608</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1608ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A01</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A02</Name>
								<BitSize>176</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A03</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Digital Inputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Velocity Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A04</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Position Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Following Position Error</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Torque Actual Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>statusword</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mode Of Operation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Padding</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A07ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A07</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A07ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A08ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>256</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>8</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1A08</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1A08ARR</Type>
									<BitSize>256</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of errors</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C00ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C10</Name>
								<BitSize>16</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SM0 PDO Assigment</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C11</Name>
								<BitSize>16</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SM1 PDO Assigment</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>480</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>30</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>496</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C12ARR</Type>
									<BitSize>480</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C13ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>560</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>35</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C13</Name>
								<BitSize>576</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C13ARR</Type>
									<BitSize>560</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>608</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Minimum delay time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle exceeded counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>SubIndex 00E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>SubIndex 00F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>SubIndex 010</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>SubIndex 011</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>SubIndex 012</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>SubIndex 013</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>SubIndex 014</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>SubIndex 015</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>SubIndex 016</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>SubIndex 017</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>SubIndex 018</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>SubIndex 019</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>SubIndex 01A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>SubIndex 01B</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>SubIndex 01C</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>SubIndex 01D</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>SubIndex 01E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>SubIndex 01F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C33</Name>
								<BitSize>608</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Minimum delay time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 cycle time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle exceeded counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>SubIndex 00E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>SubIndex 00F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>SubIndex 010</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>SubIndex 011</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>SubIndex 012</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>SubIndex 013</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>SubIndex 014</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>SubIndex 015</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>SubIndex 016</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>SubIndex 017</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>SubIndex 018</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>SubIndex 019</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>SubIndex 01A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>SubIndex 01B</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>SubIndex 01C</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>SubIndex 01D</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>SubIndex 01E</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>SubIndex 01F</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2020</Name>
								<BitSize>160</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Torque limit</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Time limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Distance limit in counts</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Detection Velocity Limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Detection Velocity Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT207B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Highest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min auxiliary  position range limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max auxiliary  position range limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2081</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Largest sub index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Serial Encoder Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profiler Data Base Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>DL Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>SDO ELMO Format Error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>MO Failed to start Reason</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>ECAM error</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT20B0</Name>
								<BitSize>304</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Main position feedback socket (CA[45])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Speed feedback socket (CA[46])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Commutation socket (CA[47])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Position reference socket (CA[68])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Speed reference socket (CA[69])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Current reference socket (CA[70])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Touch-probe socket (CA[87])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Homing 402 - capture socket (OV[54])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Additional position socket (CA[79])</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT20FC</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Clear Absolute Multi Pos</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>wo</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Reset EnDAT Error</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>wo</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2110ARR</Name>
								<BaseType>REAL</BaseType>
								<BitSize>2016</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>63</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT2110</Name>
								<BitSize>2032</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT2110ARR</Type>
									<BitSize>2016</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2202</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Extended Inputs Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Extended Inputs Logic</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Extended Inputs Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2205</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of subindexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Analog input 1</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Analog input 2</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT22A1</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Extended Outputs Value</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Extended Outputs Logic</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Extended Outputs Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT22A3</Name>
								<BitSize>64</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Drive Temperature [C]</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Drive Temperature [F]</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Sensor Temperature</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F00</Name>
								<BitSize>784</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>UI[1]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>UI[2]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>UI[3]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>UI[4]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>UI[5]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>UI[6]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>UI[7]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>UI[8]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>UI[9]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>UI[10]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>UI[11]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>UI[12]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>UI[13]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>UI[14]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>UI[15]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>UI[16]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>UI[17]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>UI[18]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>UI[19]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>UI[20]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>UI[21]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>UI[22]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>UI[23]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>UI[24]</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F01</Name>
								<BitSize>784</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>UF[1]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>UF[2]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>UF[3]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>UF[4]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>UF[5]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>UF[6]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>UF[7]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>UF[8]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>UF[9]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>UF[10]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>UF[11]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>UF[12]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>UF[13]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>UF[14]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>UF[15]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>UF[16]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>UF[17]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>UF[18]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>UF[19]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>UF[20]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>UF[21]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>UF[22]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>UF[23]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>UF[24]</Name>
									<Type>REAL</Type>
									<BitSize>32</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2F45</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>largest sub-index supported</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Low voltage level [mV]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>High voltage level [mV]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Analog sensors min amplitude [A2D^2]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>High Temperature sensing [C]</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT604AARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT604A</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT604AARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607BARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT607B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT607BARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607DARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT607DARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608FARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT608FARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6090ARR</Name>
								<BaseType>DINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6090</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6090ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6091ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6092ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6096</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Numerator</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Divisor</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6097</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of sub indexes</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Numerator</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Divisor</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT6099ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>No of elements</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation time period value</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation time index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60E3ARR</Name>
								<BaseType>SINT</BaseType>
								<BitSize>288</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>36</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT60E3</Name>
								<BitSize>304</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>number of PDOs</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT60E3ARR</Type>
									<BitSize>288</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Physical outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Bit mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device Type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010200</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error Register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1002</Index>
								<Name>Manufacturer Status Register</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x1003</Index>
								<Name>Pre-defined Error Field</Name>
								<Type>DT1003</Type>
								<BitSize>528</BitSize>
								<Info>
									<SubItem>
										<Name>Number of elements</Name>
										<Info>
											<DefaultData>10</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 005</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 006</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 007</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 008</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 009</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Manufacturer Device Name</Name>
								<Type>STRING(19)</Type>
								<BitSize>152</BitSize>
								<Info>
									<DefaultData>456C6D6F204D6F74696F6E20436F6E74726F6C</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Manufacture Hardware Version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100A</Index>
								<Name>Manufacturer Software Version</Name>
								<Type>STRING(0)</Type>
								<BitSize>0</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100B</Index>
								<Name>Bootloader Version</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1010</Index>
								<Name>Store Parameters</Name>
								<Type>DT1010</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x1011</Index>
								<Name>Restore Parameters</Name>
								<Type>DT1011</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity Object</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x10E0</Index>
								<Name>Device ID Reload</Name>
								<Type>DT10E0</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x10F1</Index>
								<Name>Sync error setting</Name>
								<Type>DT10F1</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Highest sub-index supported</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Local Error Reaction</Name>
										<Info>
											<DefaultValue>#x00000002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error counter limit</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT1600</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>2nd receive PDO-Mapping</Name>
								<Type>DT1601</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>3rd receive PDO-Mapping</Name>
								<Type>DT1602</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1603</Index>
								<Name>4th receive PDO-Mapping</Name>
								<Type>DT1603</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1604</Index>
								<Name>5th receive PDO-Mapping</Name>
								<Type>DT1604</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1605</Index>
								<Name>6th receive PDO-Mapping</Name>
								<Type>DT1605</Type>
								<BitSize>240</BitSize>
							</Object>
							<Object>
								<Index>#x1606</Index>
								<Name>7th receive PDO-Mapping</Name>
								<Type>DT1606</Type>
								<BitSize>208</BitSize>
							</Object>
							<Object>
								<Index>#x1607</Index>
								<Name>8th receive PDO-Mapping</Name>
								<Type>DT1607</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1608</Index>
								<Name>9th receive PDO-Mapping</Name>
								<Type>DT1608</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st transmit PDO-Mapping</Name>
								<Type>DT1A00</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1A01</Index>
								<Name>2nd transmit PDO-Mapping</Name>
								<Type>DT1A01</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x1A02</Index>
								<Name>3rd transmit PDO-Mapping</Name>
								<Type>DT1A02</Type>
								<BitSize>176</BitSize>
							</Object>
							<Object>
								<Index>#x1A03</Index>
								<Name>4th transmit PDO-Mapping</Name>
								<Type>DT1A03</Type>
								<BitSize>144</BitSize>
							</Object>
							<Object>
								<Index>#x1A04</Index>
								<Name>5th transmit PDO-Mapping</Name>
								<Type>DT1A04</Type>
								<BitSize>208</BitSize>
							</Object>
							<Object>
								<Index>#x1A07</Index>
								<Name>8th transmit PDO-Mapping</Name>
								<Type>DT1A07</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1A08</Index>
								<Name>9th transmit PDO-Mapping</Name>
								<Type>DT1A08</Type>
								<BitSize>272</BitSize>
							</Object>
							<Object>
								<Index>#x1C00</Index>
								<Name>SM Communication Type</Name>
								<Type>DT1C00</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x1C10</Index>
								<Name>Sync Manager 0 Communication Type</Name>
								<Type>DT1C10</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C11</Index>
								<Name>Sync Manager 1 Communication Type</Name>
								<Type>DT1C11</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C12</Index>
								<Name>Sync Manager 2 Communication Type</Name>
								<Type>DT1C12</Type>
								<BitSize>496</BitSize>
							</Object>
							<Object>
								<Index>#x1C13</Index>
								<Name>Sync Manager 3 Communication Type</Name>
								<Type>DT1C13</Type>
								<BitSize>576</BitSize>
							</Object>
							<Object>
								<Index>#x1C32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>608</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultValue>#x20</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Type</Name>
										<Info>
											<DefaultValue>#x0002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultValue>#x000F4240</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultValue>#x00000000</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync types supported</Name>
										<Info>
											<DefaultValue>#x0007</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultValue>#x0003D090</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum delay time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle exceeded counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 017</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 018</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 019</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01A</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01B</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01C</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01D</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>608</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultValue>#x20</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Type</Name>
										<Info>
											<DefaultValue>#x0002</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultValue>#x000F4240</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync types supported</Name>
										<Info>
											<DefaultValue>#x0007</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultValue>#x0003D090</DefaultValue>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum delay time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 cycle time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle exceeded counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 00F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 010</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 011</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 012</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 013</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 014</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 015</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 016</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 017</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 018</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 019</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01A</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01B</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01C</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01D</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01E</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 01F</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>Fast Reference</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x200A</Index>
								<Name>Manufacture Boot Version</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x201B</Index>
								<Name>Filtered RMS Current</Name>
								<Type>REAL</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2020</Index>
								<Name>Homing on block limits</Name>
								<Type>DT2020</Type>
								<BitSize>160</BitSize>
							</Object>
							<Object>
								<Index>#x2041</Index>
								<Name>Timestamp</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2046</Index>
								<Name>DC clock inhibit time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2060</Index>
								<Name>Parameters Checksum</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2061</Index>
								<Name>FoE Download Parameters Error</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2062</Index>
								<Name>FoE Parameters Last String Send To Drive</Name>
								<Type>STRING(64)</Type>
								<BitSize>512</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x207B</Index>
								<Name>YM[] Auxiliary Position range limit</Name>
								<Type>DT207B</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x2081</Index>
								<Name>EE[] Extendend Error</Name>
								<Type>DT2081</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>Largest sub index supported</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial Encoder Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profiler Data Base Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DL Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SDO ELMO Format Error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MO Failed to start Reason</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ECAM error</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2084</Index>
								<Name>Serial Sensor Status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2085</Index>
								<Name>Extra Status Reg</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x2086</Index>
								<Name>STO Status Reg</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2087</Index>
								<Name>PAL Version</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x20A0</Index>
								<Name>Additional position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x20B0</Index>
								<Name>Socket Additional Functions</Name>
								<Type>DT20B0</Type>
								<BitSize>304</BitSize>
							</Object>
							<Object>
								<Index>#x20E0</Index>
								<Name>Ecat alias object</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x20FC</Index>
								<Name>Absolute Sensors Functions</Name>
								<Type>DT20FC</Type>
								<BitSize>48</BitSize>
								<Info/>
							</Object>
							<Object>
								<Index>#x20FD</Index>
								<Name>Clear Digital Inputs</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2110</Index>
								<Name>KI for inner loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2111</Index>
								<Name>KP for inner loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2112</Index>
								<Name>KP for outer loop</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2113</Index>
								<Name>Vel Filt1 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2114</Index>
								<Name>Vel Filt1 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2115</Index>
								<Name>Vel Filt1 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2116</Index>
								<Name>Vel Filt1 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2117</Index>
								<Name>Vel Filt2 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2118</Index>
								<Name>Vel Filt2 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2119</Index>
								<Name>Vel Filt2 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211A</Index>
								<Name>Vel Filt2 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211B</Index>
								<Name>Pos Filt2 Param 1</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211C</Index>
								<Name>Pos Filt2 Param 2</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211D</Index>
								<Name>Pos Filt2 Param 3</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x211E</Index>
								<Name>Pos Filt2 Param 4</Name>
								<Type>DT2110</Type>
								<BitSize>2032</BitSize>
							</Object>
							<Object>
								<Index>#x2202</Index>
								<Name>Extended Inputs</Name>
								<Type>DT2202</Type>
								<BitSize>112</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Value</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Logic</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Extended Inputs Mask</Name>
										<Info>
											<DefaultData>FFFFFFFF</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2203</Index>
								<Name>App Object</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x2205</Index>
								<Name>Analog Input</Name>
								<Type>DT2205</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x2206</Index>
								<Name>Digital Supply In mV</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x22A1</Index>
								<Name>Extended Outputs</Name>
								<Type>DT22A1</Type>
								<BitSize>112</BitSize>
							</Object>
							<Object>
								<Index>#x22A3</Index>
								<Name>Temperature Array</Name>
								<Type>DT22A3</Type>
								<BitSize>64</BitSize>
							</Object>
							<Object>
								<Index>#x22A4</Index>
								<Name>Motor temperature</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E00</Index>
								<Name>Gain scheduling manual index</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E10</Index>
								<Name>Set Position On TouchProbe</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2E15</Index>
								<Name>Gantry Yaw Offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F00</Index>
								<Name>UI[] Commands</Name>
								<Type>DT2F00</Type>
								<BitSize>784</BitSize>
							</Object>
							<Object>
								<Index>#x2F01</Index>
								<Name>UF[] Commands</Name>
								<Type>DT2F01</Type>
								<BitSize>784</BitSize>
							</Object>
							<Object>
								<Index>#x2F05</Index>
								<Name>Hardware Type WS[8]</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F41</Index>
								<Name>Configuration Object</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x2F45</Index>
								<Name>ET[] Extra Threshold</Name>
								<Type>DT2F45</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>largest sub-index supported</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Low voltage level [mV]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>High voltage level [mV]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Analog sensors min amplitude [A2D^2]</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>High Temperature sensing [C]</Name>
										<Info>
											<DefaultData>5500</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2F75</Index>
								<Name>Interpolation cycle timeout</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>1</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6007</Index>
								<Name>Abort connection code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Error Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Controlword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>m</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Statusword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605A</Index>
								<Name>Quick Stop option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultValue>2</DefaultValue>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Shutdown option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Disable operation option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605D</Index>
								<Name>Halt option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Fault reaction option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of operation display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Position demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Position actual internal value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Position following error window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Following error time out</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Position window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Position window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Velocity sensor actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606A</Index>
								<Name>Velocity sensor selection code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606B</Index>
								<Name>Velocity demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Velocity actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606D</Index>
								<Name>Velocity window</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606E</Index>
								<Name>Velocity window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x606F</Index>
								<Name>Velocity threshold</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Velocity threshold time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target torque</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6072</Index>
								<Name>Maximal torque</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6073</Index>
								<Name>Maximal Current</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6074</Index>
								<Name>Torque demand</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6075</Index>
								<Name>Motor rated current</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6076</Index>
								<Name>Motor rated torque</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6078</Index>
								<Name>Current actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6079</Index>
								<Name>DC link voltage</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607B</Index>
								<Name>Position range limit</Name>
								<Type>DT607B</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x607C</Index>
								<Name>Home offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Software position limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x607E</Index>
								<Name>Polarity</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607F</Index>
								<Name>Max profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6080</Index>
								<Name>Max motor speed</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>End velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Profile acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Profile deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Quick stop deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Motion profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Torque slope</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x608F</Index>
								<Name>Position encoder resolution</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6090</Index>
								<Name>Velocity encoder resolution</Name>
								<Type>DT6090</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed constant</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6096</Index>
								<Name>Velocity factor</Name>
								<Type>DT6096</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6097</Index>
								<Name>Acceleration factor</Name>
								<Type>DT6097</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Homing method</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Homing speeds</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x609A</Index>
								<Name>Homing acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B0</Index>
								<Name>Position offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B1</Index>
								<Name>Velocity offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B2</Index>
								<Name>Torque offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B8</Index>
								<Name>Touch probe function</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B9</Index>
								<Name>Touch probe status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BA</Index>
								<Name>Touch probe pos1 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BB</Index>
								<Name>Touch probe pos1 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BC</Index>
								<Name>Touch probe pos2 pos value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BD</Index>
								<Name>Touch probe pos2 neg value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C2</Index>
								<Name>Interpolation time period</Name>
								<Type>DT60C2</Type>
								<BitSize>48</BitSize>
							</Object>
							<Object>
								<Index>#x60C5</Index>
								<Name>Max acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C6</Index>
								<Name>Max deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E3</Index>
								<Name>Supported Homing Method</Name>
								<Type>DT60E3</Type>
								<BitSize>304</BitSize>
							</Object>
							<Object>
								<Index>#x60F2</Index>
								<Name>Positioning option code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F4</Index>
								<Name>Following error actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FA</Index>
								<Name>Control effort</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FC</Index>
								<Name>Position demand internal value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Digital inputs</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Target velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported drive modes</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1800" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="#x8C" MaxSize="#x8C" DefaultSize="#x8C" StartAddress="#x1900" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm MinSize="0" MaxSize="#x20" DefaultSize="#x20" StartAddress="#x1100" ControlByte="#x64" Enable="1">Outputs</Sm>
				<Sm MinSize="0" MaxSize="#x20" DefaultSize="#x20" StartAddress="#x1180" ControlByte="#x20" Enable="1">Inputs</Sm>
				<Su>Drive</Su>
				<RxPdo Fixed="1" Sm="2">
					<Index>#x1600</Index>
					<Name>Outputs</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1601</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1602</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1603</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1604</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1605</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1605</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1606</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1606</Index>
					<Name>Outputs</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Exclude>#x1604</Exclude>
					<Exclude>#x1605</Exclude>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Offset</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1607</Index>
					<Name>Outputs</Name>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1608</Index>
					<Name>Outputs</Name>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160A</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160B</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160C</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160D</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6072</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Torque</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160E</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6073</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Max. Current</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x160F</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1611</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6081</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Velocity</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1612</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6082</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>End velocity</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1613</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6083</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Acceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1614</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6084</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Profile Deceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1615</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6087</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Torque Slope</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1616</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1617</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Offset</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1618</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Offset</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1619</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60B8</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch Probe Function</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161A</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x2E00</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Gain scheduling manual index</Name>
						<DataType>UINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161C</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161D</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x60FE</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Outputs</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161E</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x607E</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Polarity</Name>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x161F</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x6085</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Quick stop deceleration</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1620</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x22A1</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Extended Outputs </Name>
						<DataType>UDINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="1">
					<Index>#x1621</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index>#x2005</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Fast Reference</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index>#x1A00</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A01</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606B</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6074</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque demand value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A02</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A03</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A03</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A04</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A04</Index>
					<Name>Inputs</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60F4</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Following error actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A07</Index>
					<Name>Inputs</Name>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A08</Index>
					<Name>Inputs</Name>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0A</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status word</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0B</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<BitLen>8</BitLen>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0C</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6062</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Demand [UU]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0D</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6063</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Actual position [counts]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0E</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A0F</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6069</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity sensor actual value [counts/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A10</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x606B</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity demand [cnt/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A11</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A12</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6074</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque demand value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A13</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A14</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60B9</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Touch Probe status</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A15</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BA</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch Probe Pos1 Positive</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A16</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BB</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch Probe Pos1 Negative</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A17</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BC</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch probe pos2 pos value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A18</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6079</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>DC link circuit voltage</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A19</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60F4</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Following error</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1A</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FA</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Control Effort [cnt/sec]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1B</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FC</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Demand Value [cnt]</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1C</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Digital Inputs</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1D</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2205</Index>
						<SubIndex>1</SubIndex>
						<BitLen>16</BitLen>
						<Name>Analog Input 1</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1E</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x20A0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Auxiliary position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A1F</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x6078</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Current actual value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A20</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x60BD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Touch probe pos2 neg value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A21</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2085</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Extra Status Reg</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A22</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x1002</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Elmo Status Reg</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A23</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2202</Index>
						<SubIndex>1</SubIndex>
						<BitLen>32</BitLen>
						<Name>Extended Inputs Value</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A24</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x2203</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>App Object</Name>
						<DataType>UDINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="1">
					<Index>#x1A26</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index>#x603F</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Error Code</Name>
						<DataType>UINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<EoE/>
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true" CompleteAccess="false">
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
							<Comment>Op mode</Comment>
						</InitCmd>
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x60C2</Index>
							<SubIndex>1</SubIndex>
							<Data>02</Data>
							<Comment>Cycle time</Comment>
						</InitCmd>
					</CoE>
					<FoE/>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC Sync</Name>
						<Desc>DC for synchronization</Desc>
						<AssignActivate>#x0300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
					</OpMode>
					<OpMode>
						<Name>DC Off</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<ESC>
					<Reg0108>#x0</Reg0108>
					<Reg0400>#x9C2</Reg0400>
					<Reg0410>#x03E8</Reg0410>
					<Reg0420>#x2710</Reg0420>
				</ESC>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050C0304E803</ConfigData>
					<BootStrap>00188C0000198C00</BootStrap>
					<Category>
						<CatNo>30</CatNo>
						<Data>00000000002F0101000001000000000011110000000000000000000000000000</Data>
					</Category>
					<Category>
						<CatNo>41</CatNo>
						<Data>000018800026000101</Data>
					</Category>
				</Eeprom>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>
