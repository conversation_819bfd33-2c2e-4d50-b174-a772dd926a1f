/*=========================================================================

  Library:   CTK

  Copyright (c) Kitware Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0.txt

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=========================================================================*/

#ifndef __ctkErrorLogContext_h
#define __ctkErrorLogContext_h

// Qt includes
#include <QString>

// CTK includes
#include "ctkCoreExport.h"

//------------------------------------------------------------------------------
/// \ingroup Core
struct CTK_CORE_EXPORT ctkErrorLogContext
{
  ctkErrorLogContext():Line(0),File("unknown"), Function("unknown"), Message(""){}
  ctkErrorLogContext(const QString& msg):
    Line(0),File("unknown"), Function("unknown"), Message(msg){}
  QString Category;
  int Line;
  QString File;
  QString Function;
  QString Message;
};

#endif
