<?xml version="1.0" encoding="utf-8"?>
<!-- 被2012 rel. 2 () 使用XMLSpy v编辑的 (http://www.altova.com) by -->
<!-- edit  with XMLSpy v2011 rel. 2 sp1 (http://www.altova.com) by MLTOR -->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.6">
	<Vendor>
		<Id>#x000001BA</Id>
		<Name>MLTOR</Name>
		<ImageData16x14>424DA60200000000000036000000
0000100000000D000000010018000000000070020000C40E0000C40E00000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFE2CAB0AB6317F7F0E9A25300F0E5D8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3AD85A75C0C9F4D009941009235009941009B4600963D00E6D1BAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD1A97FAC6519963C00B2712BE0C6ABEFE2D4EAD8C5C59460933700C4915CE9D7C3FFFFFFFFFFFFFFFFFFFFFFFFFFFFFF9B4500973D00D6B38DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBF6F29439009C4800FFFFFFFFFFFFFFFFFFFFFFFFDDC1A29E4C00B67633FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F9FBE0DEDDE7E2DEDCDCDCAD671D994100E8CCAED6F0FF005FEBDFEDFD0062EBC0DDFA0075ED76C2FFBC6F1D9A4300B96C1A83ADD50065F3FFFFFFE9D8C5943900EAD0B4DCF2FF006EEDEDF5FE0070EDD0E6FB1482EF85CAFFC17A2DB77937FFFFFF9BCCFD006DEDFFFFFF9942009C4700D7AC7EDEF7FF0071ED8AC1F70071ED6BB0F51884EF83C9FFC17A2DB57431FFFFFFA3CEF8006BECFFFFFFEDDECFA25200A55100E3FFFF0E81F40E7FEFF4F9FE3795F10E7FEF91D7FFC17C32BD7B37D3F3FF1B86F01683EF58A6F3FFFFFF9E4C00994100B56E23FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE6BF96963C009B4600FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE7D4BFC99A69994200963D00B77938C99B6AC39059A04F00943900BD8549E5CEB7FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD7B692A35401AE69209C4700963C009B4500B16F29AF6B22E1C7ACFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6EEE7A85D0EF6EEE6A25300F2E7DBFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF</ImageData16x14>
	</Vendor>
	<Descriptions>
		<Groups>
			<Group SortOrder="0">
				<Type>ServoDrive</Type>
				<Name LcId="1033">ServoDrives</Name>
				<Image16x14>DRIVE</Image16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x26483062" RevisionNo="#x00001">TSD-E</Type>
				<Name LcId="1033">TSD-E</Name>
				<Info>
					<StateMachine>
						<Timeout>
							<PreopTimeout>2000</PreopTimeout>
							<SafeopOpTimeout>9000</SafeopOpTimeout>
							<BackToInitTimeout>5000</BackToInitTimeout>
							<BackToSafeopTimeout>200</BackToSafeopTimeout>
						</Timeout>
					</StateMachine>
					<Mailbox>
						<Timeout>
							<RequestTimeout>100</RequestTimeout>
							<ResponseTimeout>2000</ResponseTimeout>
						</Timeout>
					</Mailbox>
				</Info>
				<GroupType>ServoDrive</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>BIT2</Name>
								<BitSize>2</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>BOOL</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<!--Std type (see ETG.1020)-->
								<Name>ARRAY [0..3] OF BYTE</Name>
								<BaseType>USINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>0</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<!--Device Name (0x1008) type-->
								<Name>STRING(34)</Name>
								<BitSize>272</BitSize>
							</DataType>
							<DataType>
								<!--Hardware version string (0x1009)-->
								<Name>STRING(4)</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<!--Ident object (0x1018) type-->
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00ARR</Name>
								<BaseType>USINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C00ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Error Setting object (0x10F1) type-->
								<Name>DT10F1</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Local Error Reaction</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Sync Error Counter Limit</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Datatype for SM2(Output) Synchronisation  Parameter-->
								<Name>DT1C32</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<!--Datatype for SM3(Input) Synchronisation  Parameter-->
								<Name>DT1C33</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>m</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>c</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>SubIndex 003</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>SubIndex 004</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>SubIndex 005</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>SubIndex 006</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>SubIndex 007</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>SubIndex 007</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1601</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1602</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>240</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>SubIndex 003</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>SubIndex 004</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>SubIndex 005</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>SubIndex 006</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>SubIndex 007</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A01</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A02</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>SubIndex 001</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>SubIndex 002</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C12ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C13ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C13</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C13ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOp">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Encoder Increments</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Feed</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation period</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation Index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FF</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Target Velocity</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6502</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Supported Drive Modes</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DTF000</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Module index distance</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Maximum number of modules</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2800</Name>
								<BitSize>528</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>dP-SPd    电机转速</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>dP-PoS    位置反馈</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>dP-CPo    位置指令</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>dP-EPo    位置偏差</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>dP-I      电机实时电流0.1A</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>dP-ct     指令转矩百分比</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>dP-trq    电机负载百分比</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>dP-dU     直流母线电压</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>dP-oC     驱动器温度</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>dp-Err    驱动器报警</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>dP-16P    Err16百分比</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>dP-Ad     编码器单圈值</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>dP-AS     编码器多圈值</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>dP-SoF    软件版本</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>dP-rE1    编码器故障检测1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>dP-rE2    编码器故障检测2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>dP-rE3    编码器故障检测3</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>dP-rE4A   总线PDI丢失次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>dP-rE4b   总线同步信号丢失次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>dP-rE5A   总线接收数据错误次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>dP-rE5b   总线连接断开次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>dP-rE5c   总线PDI数据出错次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>dP-rE5d   总线数据转发出错次数</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							
								<DataType>
								<!--Error Setting object (0x10F1) type-->
								<Name>DT1010</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Store All Parameters</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							
							
							
							<DataType>
								<Name>DTF010ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DTF010</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DTF010ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<!--Module Profile Information-->
							</DataType>
							
							
							
							
							
							<DataType>
								<Name>DTF030ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DTF030</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DTF030ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<!--Module Profile Information-->
							</DataType>
							<DataType>
								<Name>DTF050ARR</Name>
								<BaseType>UDINT</BaseType>
								<BitSize>64</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>2</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DTF050</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DTF050ARR</Type>
									<BitSize>64</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<!--Module Ident list of the detected modules-->
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010200</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>m</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Device name</Name>
								<Type>STRING(34)</Type>
								<BitSize>272</BitSize>
								<Info>
									<DefaultData>454C39383030207C203241786973204369413430322053616D706C655F5635693130</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Hardware version</Name>
								<Type>STRING(4)</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>6E2E612E</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x100a</Index>
								<Name>Software version</Name>
								<Type>STRING(4)</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>352E3130</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c00</Index>
								<Name>Sync manager type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vendor ID</Name>
										<Info>
											<DefaultData>020000E0</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Product code</Name>
										<Info>
											<DefaultData>52304826</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Revision</Name>
										<Info>
											<DefaultData>11020100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial number</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x10F1</Index>
								<Name>Error Settings</Name>
								<Type>DT10F1</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Local Error Reaction</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error Counter Limit</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>1E40</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>50C30000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and Copy Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get Cycle Time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>1E40</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>50C30000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and Copy Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get Cycle Time</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 Cycle Time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM-Event Missed</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle Time Too Small</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c12</Index>
								<Name>RxPDO assign</Name>
								<Type>DT1C12</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>0016</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1c13</Index>
								<Name>TxPDO assign</Name>
								<Type>DT1C13</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>001a</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>csp/csv RxPDO</Name>
								<Type>DT1600</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>08</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>	
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>										
									<SubItem>
										<Name>SubIndex 005</Name>
										<Info>
											<DefaultData>08006060</DefaultData>
										</Info>
									</SubItem>	
									<SubItem>
										<Name>SubIndex 006</Name>
										<Info>
											<DefaultData>08000000</DefaultData>
										</Info>
									</SubItem>										
									
									
																	
									
									<SubItem>
										<Name>SubIndex 007</Name>
										<Info>
											<DefaultData>1000B260</DefaultData>
										</Info>
									</SubItem>									
									<SubItem>
										<Name>SubIndex 008</Name>
										<Info>
											<DefaultData>1000FE60</DefaultData>
										</Info>
									</SubItem>																		
									
																	
									



								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>csp RxPDO</Name>
								<Type>DT1601</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>csv RxPDO</Name>
								<Type>DT1602</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1a00</Index>
								<Name>csp/csv TxPDO</Name>
								<Type>DT1A00</Type>
								<BitSize>240</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>07</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>									
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>									
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>		

									<SubItem>
										<Name>SubIndex 005</Name>
										<Info>
											<DefaultData>08006160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 006</Name>
										<Info>
											<DefaultData>08000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 007</Name>
										<Info>
											<DefaultData>1000FD60</DefaultData>
										</Info>
									</SubItem>	

								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1a01</Index>
								<Name>csp TxPDO</Name>
								<Type>DT1A01</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1a02</Index>
								<Name>csv TxPDO</Name>
								<Type>DT1A02</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Error Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Error Code</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Control Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Control Word</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target Torque</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Target Torque</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>							
							
							<Object>
								<Index>#x0000</Index>
								<Name>RES</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<SubItem>
										<Name>RES</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							
							<Object>
								<Index>#x60B2</Index>
								<Name>Control Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Target Torque</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>IO_IN</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>IO_IN</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							
							
							
							
							
							<Object>
								<Index>#x6041</Index>
								<Name>Status Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Status Word</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Status Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>IO_OUT</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>							
							
							
							<Object>
								<Index>#x605A</Index>
								<Name>Quickstop Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Quickstop Option Code</Name>
										<Info>
											<DefaultData>2</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Shutdown Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Shutdown Option Code</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Disable Operation Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Disable Operation Option Code</Name>
										<Info>
											<DefaultData>1</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Fault Reaction Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Fault Reaction Code</Name>
										<Info>
											<DefaultData>2</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of Operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<SubItem>
										<Name>Modes of Operation</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of Operation Display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<SubItem>
										<Name>Modes of Operation Display</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position Actual Value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Position Actual Value</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Velocity Actual Value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Velocity Actual Value</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque Actual Value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<SubItem>
										<Name>Torque Actual Value</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target Position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Target Position</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Software Position Limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min position limit</Name>
										<Info>
											<DefaultData>006CCA88</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Max position limit</Name>
										<Info>
											<DefaultData>00943577</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x607E</Index>
								<Name>Polarity</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Quickstop Declaration</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Quickstop Declaration</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x608F</Index>
								<Name>Position Encoder Resolution</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Encoder Increments</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Motor Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear Ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Shaft Revolutions</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed Constant</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Feed</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C2</Index>
								<Name>Interpolation Time Period</Name>
								<Type>DT60C2</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>2</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation period</Name>
										<Info>
											<DefaultData>1</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation Index</Name>
										<Info>
											<DefaultData>-3</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Target Velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Target Velocity</Name>
										<Info>
											<DefaultData>0</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported Drive Modes</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Supported Drive Modes</Name>
										<Info>
											<DefaultData>256</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf000</Index>
								<Name>Modular device profile</Name>
								<Type>DTF000</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Module index distance</Name>
										<Info>
											<DefaultData>800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Maximum number of modules</Name>
										<Info>
											<DefaultData>2</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf010</Index>
								<Name>Module profile list</Name>
								<Type>DTF010</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>92010200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf030</Index>
								<Name>Configured module Ident list</Name>
								<Type>DTF030</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>00983100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#xf050</Index>
								<Name>Module detected list</Name>
								<Type>DTF050</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>00983100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x8021</Index>
								<Name>Var0x8021 PARA</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2001</Index>
								<Name>PA1  #电机型号代码</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2004</Index>
								<Name>PA4  #控制方式</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>PA5  #速度环比例增益</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>PA6  #速度环积分时间常数</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>PA7  #电流指令低通滤波器系数</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2008</Index>
								<Name>PA8  #速度反馈低通滤波器系数</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2009</Index>
								<Name>PA9  #位置环比例增益</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x200A</Index>
								<Name>PA10 #位置环前馈增益</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x200B</Index>
								<Name>PA11 #位置指令前馈低通滤波器截止频率</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2027</Index>
								<Name>PA39 #自动识别电机型号功能选择</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2028</Index>
								<Name>PA40 #惯量比</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2029</Index>
								<Name>PA41 #刚性</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x208C</Index>
								<Name>PA140#电流环比例增益</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x208D</Index>
								<Name>PA141#电流环积分时间常数</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6A</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2800</Index>
								<Name>伺服状态监视</Name>
								<Type>DT2800</Type>
								<BitSize>528</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Module index distance</Name>
										<Info>
											<DefaultData>800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Maximum number of modules</Name>
										<Info>
											<DefaultData>2</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1010</Index>
								<Name>Store Parameters</Name>
								<Type>DT1010</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Store Parameters</Name>
										<Info>
											<DefaultData>1</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Fmmu>MBoxState</Fmmu>
				<Sm MinSize="34" MaxSize="192" DefaultSize="128" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="34" MaxSize="192" DefaultSize="128" StartAddress="#x1100" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="0" StartAddress="#x1200" ControlByte="#x64" Enable="1">Outputs</Sm>
				<Sm DefaultSize="0" StartAddress="#x1300" ControlByte="#x20" Enable="1">Inputs</Sm>
				<Mailbox DataLinkLayer="true">
					<CoE SdoInfo="true" SegmentedSdo="true" CompleteAccess="true" PdoAssign="true"/>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC</Name>
						<Desc>DC-Synchron</Desc>
						<AssignActivate>#x300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="1">0</CycleTimeSync1>
					</OpMode>
					<OpMode>
						<Name>Synchron</Name>
						<Desc>SM-Synchron</Desc>
						<AssignActivate>#x0</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="1">0</CycleTimeSync1>
					</OpMode>
				</Dc>
				<Slots SlotIndexIncrement="#x800" SlotPdoIncrement="16">
					<Slot MinInstances="1" MaxInstances="1">
						<Name>Axis 0</Name>
						<ModuleIdent Default="0">#x119800</ModuleIdent>
						<ModuleIdent Default="0">#x219800</ModuleIdent>
						<ModuleIdent Default="1">#x319800</ModuleIdent>
					</Slot>
					<Slot MinInstances="0" MaxInstances="1">
						<Name>Axis 1</Name>
						<ModuleIdent Default="0">#x119800</ModuleIdent>
						<ModuleIdent Default="0">#x219800</ModuleIdent>
						<ModuleIdent Default="0">#x319800</ModuleIdent>
					</Slot>
				</Slots>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>050000cc64000000000000000000</ConfigData>
				</Eeprom>
				<ImageData16x14>424DA6020000000000003600000028000000100000000D000000010018000000000070020000C40E0000C40E00000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFE2CAB0AB6317F7F0E9A25300F0E5D8FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3AD85A75C0C9F4D009941009235009941009B4600963D00E6D1BAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD1A97FAC6519963C00B2712BE0C6ABEFE2D4EAD8C5C59460933700C4915CE9D7C3FFFFFFFFFFFFFFFFFFFFFFFFFFFFFF9B4500973D00D6B38DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBF6F29439009C4800FFFFFFFFFFFFFFFFFFFFFFFFDDC1A29E4C00B67633FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8F9FBE0DEDDE7E2DEDCDCDCAD671D994100E8CCAED6F0FF005FEBDFEDFD0062EBC0DDFA0075ED76C2FFBC6F1D9A4300B96C1A83ADD50065F3FFFFFFE9D8C5943900EAD0B4DCF2FF006EEDEDF5FE0070EDD0E6FB1482EF85CAFFC17A2DB77937FFFFFF9BCCFD006DEDFFFFFF9942009C4700D7AC7EDEF7FF0071ED8AC1F70071ED6BB0F51884EF83C9FFC17A2DB57431FFFFFFA3CEF8006BECFFFFFFEDDECFA25200A55100E3FFFF0E81F40E7FEFF4F9FE3795F10E7FEF91D7FFC17C32BD7B37D3F3FF1B86F01683EF58A6F3FFFFFF9E4C00994100B56E23FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE6BF96963C009B4600FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE7D4BFC99A69994200963D00B77938C99B6AC39059A04F00943900BD8549E5CEB7FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD7B692A35401AE69209C4700963C009B4500B16F29AF6B22E1C7ACFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6EEE7A85D0EF6EEE6A25300F2E7DBFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF</ImageData16x14>
			</Device>
		</Devices>
		<Modules>
			<Module>
				<Type ModuleIdent="#x119800"> Axis (csv,csp)</Type>
				<Name>dynamic switchbewteen csp/csv</Name>
				<RxPdo Fixed="1" Sm="2">
					<Index DependOnSlot="true">#x1600</Index>
					<Name>Outputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control Word</Name>
						<Comment>object 0x6040:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>TargetPosition</Name>
						<Comment>object 0x607A:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>TargetVelocity</Name>
						<Comment>object 0x60FF:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<Comment>object 0x6071:0</Comment>
						<DataType>UINT</DataType>
					</Entry>					

					<Entry>
						<Index DependOnSlot="true">#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>ModeOfOperation</Name>
						<Comment>object 0x6060:0</Comment>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>RES</Name>
						<Comment>object 0x0000:0</Comment>
						<DataType>USINT</DataType>
					</Entry>
					
					
					<Entry>
						<Index DependOnSlot="true">#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque OffSet</Name>
						<Comment>object 0x60B2:0</Comment>
						<DataType>UINT</DataType>
					</Entry>					
					<Entry>
						<Index DependOnSlot="true">#x60FE</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>IO_IN</Name>
						<Comment>object 0x60FE:0</Comment>
						<DataType>UINT</DataType>
					</Entry>					
					

				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index DependOnSlot="true">#x1a00</Index>
					<Name>Inputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status Word</Name>
						<Comment>object 0x6041:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>ActualPosition</Name>
						<Comment>object 0x6064:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>ActualVelocity</Name>
						<Comment>object 0x606C:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Actual Torque</Name>
						<Comment>object 0x6077:0</Comment>
						<DataType>UINT</DataType>
					</Entry>					
					
					
					<Entry>
						<Index DependOnSlot="true">#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>ModeOfOperationDisplay</Name>
						<Comment>object 0x6061:0</Comment>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index>#x0</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>RES</Name>
						<Comment>object 0x0000:0</Comment>
						<DataType>USINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x60FD</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>IO_OUT</Name>
						<Comment>object 0x60FD:0</Comment>
						<DataType>UINT</DataType>
					</Entry>					
					
					
					
					
				</TxPdo>
				<Profile>
					<ProfileNo>402</ProfileNo>
				</Profile>
			</Module>
			<Module>
				<Type ModuleIdent="#x219800">csp - axis</Type>
				<Name>Axis only supports csp</Name>
				<RxPdo Fixed="1" Sm="2">
					<Index DependOnSlot="true">#x1601</Index>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1602</Exclude>
					<Name>Outputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control Word</Name>
						<Comment>object 0x6040:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>TargetPosition</Name>
						<Comment>object 0x607A:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index DependOnSlot="true">#x1a01</Index>
					<Exclude>#x1a00</Exclude>
					<Exclude>#x1a02</Exclude>
					<Name>Inputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status Word</Name>
						<Comment>object 0x6041:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>ActualPosition</Name>
						<Comment>object 0x6064:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<CoE>
						<InitCmd>
							<Transition>SO</Transition>
							<Index DependOnSlot="true">#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
						</InitCmd>
					</CoE>
				</Mailbox>
				<Profile>
					<ProfileNo>402</ProfileNo>
				</Profile>
			</Module>
			<Module>
				<Type ModuleIdent="#x319800">csv -  axis</Type>
				<Name>Axis only supports csv</Name>
				<RxPdo Fixed="1" Sm="2">
					<Index DependOnSlot="true">#x1602</Index>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Name>Outputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Control Word</Name>
						<Comment>object 0x6040:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<Comment>object 0x60FF:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="1" Sm="3">
					<Index DependOnSlot="true">#x1a02</Index>
					<Exclude>#x1a00</Exclude>
					<Exclude>#x1a01</Exclude>
					<Name>Inputs</Name>
					<Entry>
						<Index DependOnSlot="true">#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Status Word</Name>
						<Comment>object 0x6041:0</Comment>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index DependOnSlot="true">#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>ActualPosition</Name>
						<Comment>object 0x6064:0</Comment>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox>
					<CoE>
						<InitCmd>
							<Transition>SO</Transition>
							<Index DependOnSlot="true">#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>09</Data>
						</InitCmd>
					</CoE>
				</Mailbox>
				<Profile>
					<ProfileNo>402</ProfileNo>
				</Profile>
			</Module>
		</Modules>
	</Descriptions>
</EtherCATInfo>
