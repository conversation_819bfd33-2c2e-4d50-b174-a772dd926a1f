﻿project(APP VERSION 0.1 LANGUAGES CXX)

string(REGEX REPLACE "(.*)/(.*)" "\\1" PROJECT_CTK_DIR  ${PROJECT_SOURCE_DIR})

set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${PROJECT_CTK_DIR}/bin/cmake/debug)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${PROJECT_CTK_DIR}/bin/cmake/release)

string(REGEX REPLACE "(.*)/(.*)" "\\1" PROJECT_CTK_DIR  ${PROJECT_SOURCE_DIR})

include_directories(${PROJECT_CTK_DIR}/Libs/Core ${PROJECT_CTK_DIR}/Libs/PluginFramework)
link_directories(debug ${PROJECT_CTK_DIR}/bin/cmake/debug
    optimized ${PROJECT_CTK_DIR}/bin/cmake/release)

find_package(Qt5 REQUIRED COMPONENTS Widgets Core Gui)

add_executable(${PROJECT_NAME} "main.cpp")

add_dependencies(${PROJECT_NAME} PluginFramework Core)
target_link_libraries(${PROJECT_NAME} Qt5::Core Qt5::Gui Qt5::Widgets libPluginFramework.dll.a libCore.dll.a)

set(EXECUTABLE_OUTPUT_PATH ${PROJECT_CTK_DIR}/bin/cmake)

