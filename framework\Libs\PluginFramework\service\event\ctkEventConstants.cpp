/*=============================================================================

  Library: CTK

  Copyright (c) German Cancer Research Center,
    Division of Medical and Biological Informatics

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=============================================================================*/

#include "ctkEventConstants.h"


const QString ctkEventConstants::EVENT_TOPIC = "event.topics";
const QString ctkEventConstants::EVENT_FILTER = "event.filter";

const QString ctkEventConstants::EVENT_DELIVERY = "event.delivery";
const QString ctkEventConstants::DELIVERY_ASYNC_ORDERED = "async.ordered";
const QString ctkEventConstants::DELIVERY_ASYNC_UNORDERED = "async.unordered";

const QString ctkEventConstants::PLUGIN_SYMBOLICNAME = "plugin.symbolicName";
const QString ctkEventConstants::PLUGIN_ID = "plugin.id";
const QString ctkEventConstants::PLUGIN = "plugin";
const QString ctkEventConstants::PLUGIN_VERSION = "plugin.version";

const QString ctkEventConstants::EVENT = "event";
const QString ctkEventConstants::EXCEPTION = "exception";
const QString ctkEventConstants::EXCEPTION_CLASS = "exception.class";
const QString ctkEventConstants::EXCEPTION_MESSAGE = "exception.message";
const QString ctkEventConstants::MESSAGE = "message";
const QString ctkEventConstants::SERVICE = "service";
const QString ctkEventConstants::SERVICE_ID = "service.id";
const QString ctkEventConstants::SERVICE_OBJECTCLASS = "service.objectClass";
const QString ctkEventConstants::SERVICE_PID = "service.pid";
const QString ctkEventConstants::TIMESTAMP = "timestamp";
