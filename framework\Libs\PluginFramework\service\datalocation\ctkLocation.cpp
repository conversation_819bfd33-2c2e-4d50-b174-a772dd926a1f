/*=============================================================================

  Library: CTK

  Copyright (c) German Cancer Research Center,
    Division of Medical and Biological Informatics

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=============================================================================*/

#include "ctkLocation.h"

const QString ctkLocation::INSTANCE_FILTER = QString("(&(objectClass=") +
                                             qobject_interface_iid<ctkLocation*>() +
                                             ")(type=ctk.instance.area))";

const QString ctkLocation::INSTALL_FILTER = QString("(&(objectClass=") +
                                            qobject_interface_iid<ctkLocation*>() +
                                            ")(type=ctk.install.area))";

const QString ctkLocation::CONFIGURATION_FILTER = QString("(&(objectClass=") +
                                                  qobject_interface_iid<ctkLocation*>() +
                                                  ")(type=ctk.configuration.area))";

const QString ctkLocation::USER_FILTER = QString("(&(objectClass=") +
                                         qobject_interface_iid<ctkLocation*>() +
                                         ")(type=ctk.user.area))";

const QString ctkLocation::CTK_HOME_FILTER = QString("(&(objectClass=") +
                                             qobject_interface_iid<ctkLocation*>() +
                                             ")(type=ctk.home.location))";
