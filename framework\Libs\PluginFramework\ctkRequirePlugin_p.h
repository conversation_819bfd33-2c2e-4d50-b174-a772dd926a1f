/*=============================================================================

  Library: CTK

  Copyright (c) German Cancer Research Center,
    Division of Medical and Biological Informatics

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=============================================================================*/

#ifndef CTKREQUIREPLUGIN_P_H
#define CTKREQUIREPLUGIN_P_H

#include <ctkVersionRange_p.h>


class ctkPluginPrivate;

/**
 * \ingroup PluginFramework
 */
class ctkRequirePlugin
{

public:

  const QString name;
  const QString resolution;
  const ctkVersionRange pluginRange;

  ctkRequirePlugin(ctkPluginPrivate* requestor,
                   const QString& name, const QString& res,
                   const QString& range);

  bool overlap(const ctkRequirePlugin& rp) const;

};


#endif // CTKREQUIREPLUGIN_P_H
