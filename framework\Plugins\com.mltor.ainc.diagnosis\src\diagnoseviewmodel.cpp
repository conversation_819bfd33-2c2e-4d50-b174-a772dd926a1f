#include "diagnoseviewmodel.h"

#include "legacy/nckservermanager.h"
#include "legacy/nckserverbackend.h"
#include "legacy/parameterbackend.h"

#define HANDLE_LOG_COL_CNT 2
#define ALARM_LOG_CNT 3     // 轮转日志文件数
#define MAX_LOG_ENTRIES 100 // 触发轮转操作的消息条目（数量太大会导致前端表格操作卡顿）
#define CUR_POS_INDEX 7 //代替cursorPosition::index

DiagnoseViewModel::DiagnoseViewModel(QObject *parent)
    : QObject(parent)
    , _bitIndex(7)
{
    _alarmLog = new LogStruct();
    _warningLog = new LogStruct();

    _baseTime = QDateTime::currentMSecsSinceEpoch();
}

int DiagnoseViewModel::MasterRunStatus() 
{
   return Context::instance()->nckServerManager()->getNCKServerBackend(0)->getMasterRunInfo("RunStatus").toInt();
}

int DiagnoseViewModel::SlaveNum()
{
    return Context::instance()->nckServerManager()->getNCKServerBackend(0)->getMasterRunInfo("SlaveNum").toInt();
}

int DiagnoseViewModel::SlavePosition()
{

}

int DiagnoseViewModel::SlaveVenderId()
{

}

int DiagnoseViewModel::SlaveProductCode()
{

}

QString DiagnoseViewModel::SlaveName()
{

}

int DiagnoseViewModel::SlaveAlias()
{

}

int DiagnoseViewModel::SlaveCmdAck()
{
_nckServerBackend->getSlaveCmdInfo("ACK").toInt();
}

int DiagnoseViewModel::SlaveCmdErrCode()
{
_nckServerBackend->getSlaveCmdInfo("ErrCode").toInt();
}

QByteArray DiagnoseViewModel::SlaveCmdRegInfoValue()
{
    _nckServerBackend->getSlaveCmdInfo("RegInfoValue").toByteArray();
}

int DiagnoseViewModel::WriteSlaveCmd()
{
    
}
    

bool DiagnoseViewModel::IsEtherCATActive()
{
    return ((Context::instance()->parameterBackend()->getParameterValue("SYS_PUBLIC_CONF").toUInt() & PARA_BIT22) ? true : false);
}

QAbstractItemModel* DiagnoseViewModel::AlarmMessagemodel()
{
    return _AlarmMessagemodel;
}

QPointer<EthercatModel> DiagnoseViewModel::EcatModel()
{
    return _EcatModel;
}

QMap<QString, QVariant> DiagnoseViewModel::Ecatmap()
{
    return _Ecatmap;
}

int DiagnoseViewModel::Logid()
{
    return _Logid;
}

QString DiagnoseViewModel::Logmessage()
{
    return _Logmessage;
}

QDateTime DiagnoseViewModel::Logdatetime()
{
    return _Logdatetime;
}

int DiagnoseViewModel::Logtype()
{
    return _Logtype;
}

int DiagnoseViewModel::Logchannel()
{
    return _Logchannel;
}

QString DiagnoseViewModel::Logfile()
{
    return _Logfile;
}

int DiagnoseViewModel::Logstate()
{
    return _Logstate;
}

QFile* DiagnoseViewModel::MachingFile()
{
    return _MachingFile;
}

QStringList DiagnoseViewModel::Operfile()
{
    return _Operfile;
}

QDir DiagnoseViewModel::Machingdir()
{
    return _Machingdir;
}

QDir DiagnoseViewModel::Operdir()
{
    return _Operdir;
}

QJsonDocument* DiagnoseViewModel::mJsonDoc()
{
    return _mJsonDoc;
}

QPointer<MVLogModel> DiagnoseViewModel::mvlogmodel()
{
    return _mvlogmodel;
}

LogStruct* DiagnoseViewModel::alarmLog()
{
    return _alarmLog;
}

LogStruct* DiagnoseViewModel::warningLog()
{
    return _warningLog;
}

QString DiagnoseViewModel::ProcessData()
{
    return _ProcessData;
}

QString DiagnoseViewModel::SlaveInfo()
{
    return _SlaveInfo;
}

QString DiagnoseViewModel::SalveList()
{
    return _SalveList;
}

QString DiagnoseViewModel::PcsDataMap()
{
    return _PcsDataMap;
}

SystemDiagnoseModel* DiagnoseViewModel::SysDigModel()
{
    return _SysDigModel;
}

QString DiagnoseViewModel::weekday()
{
    return _weekday;
}

QString DiagnoseViewModel::date()
{
    return _date;
}

QString DiagnoseViewModel::time()
{
    return _time;
}

QStringList DiagnoseViewModel::OperstrList()
{
    return _OperstrList;
}

QString DiagnoseViewModel::symbolLab()
{
    return _symbolLab;
}

QString DiagnoseViewModel::textLab()
{
    return _textLab;
}

int DiagnoseViewModel::index()
{
    return _index;
}

QString DiagnoseViewModel::slaveName()
{
    return _slaveName;
}

QString DiagnoseViewModel::EditData()
{
    return _EditData;
}

void DiagnoseViewModel::setAlarmMessagemodel(QAbstractItemModel* value)
{
    if (_AlarmMessagemodel != value) {
        _AlarmMessagemodel = value;
        emit AlarmMessagemodelChanged();
    }
}

void DiagnoseViewModel::setEcatModel(QPointer<EthercatModel> value)
{
    if (_EcatModel != value) {
        _EcatModel = value;
        emit EcatModelChanged();
    }
}

void DiagnoseViewModel::setEcatmap(QMap<QString, QVariant> value)
{
    if (_Ecatmap != value) {
        _Ecatmap = value;
        emit EcatmapChanged();
    }
}

void DiagnoseViewModel::setLogid(int value)
{
    if (_Logid != value) {
        _Logid = value;
        emit LogidChanged();
    }
}

void DiagnoseViewModel::setLogmessage(QString value)
{
    if (_Logmessage != value) {
        _Logmessage = value;
        emit LogmessageChanged();
    }
}

void DiagnoseViewModel::setLogdatetime(QDateTime value)
{
    if (_Logdatetime != value) {
        _Logdatetime = value;
        emit LogdatetimeChanged();
    }
}

void DiagnoseViewModel::setLogtype(int value)
{
    if (_Logtype != value) {
        _Logtype = value;
        emit LogtypeChanged();
    }
}

void DiagnoseViewModel::setLogchannel(int value)
{
    if (_Logchannel != value) {
        _Logchannel = value;
        emit LogchannelChanged();
    }
}

void DiagnoseViewModel::setLogfile(QString value)
{
    if (_Logfile != value) {
        _Logfile = value;
        emit LogfileChanged();
    }
}

void DiagnoseViewModel::setLogstate(int value)
{
    if (_Logstate != value) {
        _Logstate = value;
        emit LogstateChanged();
    }
}

void DiagnoseViewModel::setMachingFile(QFile* value)
{
    if (_MachingFile != value) {
        _MachingFile = value;
        emit MachingFileChanged();
    }
}

void DiagnoseViewModel::setOperfile(QStringList value)
{
    if (_Operfile != value) {
        _Operfile = value;
        emit OperfileChanged();
    }
}

void DiagnoseViewModel::setMachingdir(QDir value)
{
    if (_Machingdir != value) {
        _Machingdir = value;
        emit MachingdirChanged();
    }
}

void DiagnoseViewModel::setOperdir(QDir value)
{
    if (_Operdir != value) {
        _Operdir = value;
        emit OperdirChanged();
    }
}

void DiagnoseViewModel::setmJsonDoc(QJsonDocument* value)
{
    if (_mJsonDoc != value) {
        _mJsonDoc = value;
        emit mJsonDocChanged();
    }
}

void DiagnoseViewModel::setalarmLog(LogStruct* value)
{
    if (_alarmLog != value) {
        _alarmLog = value;
        emit alarmLogChanged();
    }
}

void DiagnoseViewModel::setwarningLog(LogStruct* value)
{
    if (_warningLog != value) {
        _warningLog = value;
        emit warningLogChanged();
    }
}

void DiagnoseViewModel::setProcessData(QString value)
{
    _ProcessData = value;
    emit ProcessDataChanged();
}

void DiagnoseViewModel::setSlaveInfo(QString value)
{
    _SlaveInfo = value;
    emit SlaveInfoChanged();
}

void DiagnoseViewModel::setSalveList(QString value)
{
    _SalveList = value;
    emit SalveListChanged();
}

void DiagnoseViewModel::setPcsDataMap(QString value)
{
    _PcsDataMap = value;
    emit PcsDataMapChanged();
}

void DiagnoseViewModel::setSysDigModel(SystemDiagnoseModel* value)
{
    if (_SysDigModel != value) {
        _SysDigModel = value;
        emit SysDigModelChanged();
    }
}

void DiagnoseViewModel::setweekday(QString value)
{
    if (_weekday != value) {
        _weekday = value;
        emit weekdayChanged();
    }
}

void DiagnoseViewModel::setdate(QString value)
{
    if (_date != value) {
        _date = value;
        emit dateChanged();
    }
}

void DiagnoseViewModel::settime(QString value)
{
    if (_time != value) {
        _time = value;
        emit timeChanged();
    }
}

void DiagnoseViewModel::setOperstrList(QStringList value)
{
    if (_OperstrList != value) {
        _OperstrList = value;
        emit OperstrListChanged();
    }
}

void DiagnoseViewModel::setsymbolLab(QString value)
{
    if (_symbolLab != value) {
        _symbolLab = value;
        emit symbolLabChanged();
    }
}

void DiagnoseViewModel::settextLab(QString value)
{
    if (_textLab != value) {
        _textLab = value;
        emit textLabChanged();
    }
}

void DiagnoseViewModel::setindex(int value)
{
    if (_index != value) {
        _index = value;
        emit indexChanged();
    }
}

void DiagnoseViewModel::setslaveName(QString value)
{
    if (_slaveName != value) {
        _slaveName = value;
        emit slaveNameChanged();
    }
}

void DiagnoseViewModel::setEditData(QString value)
{
    if (_EditData != value) {
        _EditData = value;
        emit EditDataChanged();
    }
}

void DiagnoseViewModel::setBitIndex(int bitIndex)
{
    if (_bitIndex == bitIndex)
        return;

    _bitIndex = bitIndex;
    emit bitIndexChanged(bitIndex);
}

void DiagnoseViewModel::getEcatmap()
{
    auto nckServerBackend = Context::instance()->nckServerManager()->getNCKServerBackend();
    quint64 interval = QDateTime::currentMSecsSinceEpoch() - _baseTime;
    uint status = nckServerBackend->getMasterRunInfo("RunStatus").toInt();
    if(status == 2 || status == 3) {
        _Ecatmap.insert(tr("主站连接从站数"), nckServerBackend->getMasterRunInfo("SlaveNum").toInt());
        _Ecatmap.insert(tr("总线运行状态"), nckServerBackend->getMasterRunInfo("RunStatus").toInt());
        _Ecatmap.insert(tr("主站报警代码"), nckServerBackend->getMasterRunInfo("MasterError").toInt());
        _Ecatmap.insert(tr("周期数据出错计数"), nckServerBackend->getMasterRunInfo("ErrCount").toInt());
        int32 kernelIOError = nckServerBackend->getMasterRunInfo("KernelIOError").toInt();
        QString kernelIOErrText = (kernelIOError < 0) ? QString("%1 [%2]").arg(kernelIOError)
                                                                          .arg(kernelErrMap.value(kernelIOError))
                                                      : QString::number(kernelIOError);
        _Ecatmap.insert(tr("内核接口出错代码"), kernelIOErrText);
        _Ecatmap.insert(tr("系统运行时间"), formatTime(interval));
        _Ecatmap.insert(tr("主站修改时间"), getModifiedTime());
    }
}

QString DiagnoseViewModel::formatTime(quint64 ms)
{
    uint64 ss = 1000;
    uint64 mi = ss * 60;
    uint64 hh = mi * 60;
    uint64 dd = hh * 24;

    uint64 day = ms / dd;
    uint64 hour = (ms - day * dd) / hh;
    uint64 minute = (ms - day * dd - hour * hh) / mi;
    uint64 second = (ms - day * dd - hour * hh - minute * mi) / ss;

    QString days = QString::number(day,10);
    QString hou = QString::number(hour,10);
    QString min = QString::number(minute,10);
    QString sec = QString::number(second,10);

    return days + "日" + hou + "时" + min + "分" + sec + "秒";
}

QString DiagnoseViewModel::getModifiedTime()
{
    QFileInfo fileInfo("/lib/modules/3.14.17-xenomai-r2/ethercat/master/ec_master.ko");
    if(fileInfo.exists()) {
        return fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss");
    } else {
        return "--";
    }
}

void DiagnoseViewModel::OperInit()
{
    setOperdir(Context::instance()->globalVars()->GetUserDiskDir());
    Operdir().mkpath(Context::instance()->globalVars()->GetUserDiskDir()+QString("/Log"));

    for(int i = 0; i < LOG_COUNT; i++ )
    {

        _Operfile << Context::instance()->globalVars()->GetUserDiskDir()
                     + QString("/Log/OperationLog%1.csv").arg(QString::number(i+1));
    }

}

QList<QFile*> DiagnoseViewModel::getFiles()
{
    QList<QFile*> _files;

    QFile *file1 = new QFile(_Operfile[0]);
    QFile *file2 = new QFile(_Operfile[1]);
    QFile *file3 = new QFile(_Operfile[2]);
    _files = {file1, file2, file3};

    return _files;
}

void DiagnoseViewModel::readProcessData()
{
    QProcess process;

    QByteArray all;
    QByteArray err;

    connect(&process, &QProcess::readyReadStandardOutput, this, [=, &process, &all](){
        all.append(process.readAll());
    });

    connect(&process, &QProcess::readyReadStandardError, this, [=, &process, &err](){
        err.append(process.readAllStandardError());
    });

    connect(&process, static_cast<void(QProcess::*)(int, QProcess::ExitStatus)>(&QProcess::finished),
            this, [=, &process, &all, &err](int exitCode, QProcess::ExitStatus exitStatus){
        _ProcessData = (exitStatus == QProcess::NormalExit) ? QString(all) : "err:" + QString(err);
        setProcessData(_ProcessData);
        process.close();
    });

    process.start("ethercat domains -v");
    process.waitForStarted(3000);
    process.waitForFinished(-1);
}

void DiagnoseViewModel::readSlaveInfo()
{
    QProcess process;

    QByteArray all;
    QByteArray err;

    connect(&process, &QProcess::readyReadStandardOutput, this, [=, &process, &all](){
        all.append(process.readAll());
    });

    connect(&process, &QProcess::readyReadStandardError, this, [=, &process, &err](){
        err.append(process.readAllStandardError());
    });

    connect(&process, static_cast<void(QProcess::*)(int, QProcess::ExitStatus)>(&QProcess::finished),
            this, [=, &process, &all, &err](int exitCode, QProcess::ExitStatus exitStatus){
        _SlaveInfo = (exitStatus == QProcess::NormalExit) ? QString(all)
                                                          : "err:" + QString(err);
        setSlaveInfo(_SlaveInfo);
        process.close();
    });

    process.start("ethercat slaves -v");
    process.waitForStarted(3000);
    process.waitForFinished(-1);
}

void DiagnoseViewModel::readSalveList()
{
    QProcess process;

    QByteArray all;
    QByteArray err;

    connect(&process, &QProcess::readyReadStandardOutput, this, [=, &process, &all](){
        all.append(process.readAll());
    });

    connect(&process, &QProcess::readyReadStandardError, this, [=, &process, &err](){
        err.append(process.readAllStandardError());
    });

    connect(&process, static_cast<void(QProcess::*)(int, QProcess::ExitStatus)>(&QProcess::finished),
            this, [=, &process, &all, &err](int exitCode, QProcess::ExitStatus exitStatus){
        _SalveList = (exitStatus == QProcess::NormalExit) ? QString(all)
                                                          : "err:" + QString(err);
        setSalveList(_SalveList);
        process.close();
    });

    process.start("ethercat slaves");
    process.waitForStarted(3000);
    process.waitForFinished(-1);
}

void DiagnoseViewModel::readProcessDataMap()
{
    QProcess process;

    QByteArray all;
    QByteArray err;

    connect(&process, &QProcess::readyReadStandardOutput, this, [=, &process, &all](){
        all.append(process.readAll());
    });

    connect(&process, &QProcess::readyReadStandardError, this, [=, &process, &err](){
        err.append(process.readAllStandardError());
    });

    connect(&process, static_cast<void(QProcess::*)(int, QProcess::ExitStatus)>(&QProcess::finished),
            this, [=, &process, &all, &err](int exitCode, QProcess::ExitStatus exitStatus){
        _PcsDataMap = (exitStatus == QProcess::NormalExit) ? QString(all)
                                                           : "err:" + QString(err);
        setPcsDataMap(_PcsDataMap);
        process.close();
    });

    process.start("ethercat pdos");
    process.waitForStarted(3000);
    process.waitForFinished(-1);
}

void DiagnoseViewModel::LogInit()
{
    QDir createdir(Context::instance()->globalVars()->GetUserDiskDir());
    createdir.mkpath(Context::instance()->globalVars()->GetUserDiskDir() + QString("/Log"));


    for(int i = 0; i < ALARM_LOG_CNT; i++ )
    {
        _alarmLog->_filePath << Context::instance()->globalVars()->GetUserDiskDir()
                                + QString("/Log/AlarmLog") + QString::number(i+1);
        _warningLog->_filePath << Context::instance()->globalVars()->GetUserDiskDir()
                                  + QString("/Log/WarningLog") + QString::number(i+1);
    }

    QFile *alarmFile1 = new QFile(_alarmLog->_filePath[0]);
    QFile *alarmFile2 = new QFile(_alarmLog->_filePath[1]);
    QFile *alarmFile3 = new QFile(_alarmLog->_filePath[2]);
    _alarmLog->_files = {alarmFile1, alarmFile2, alarmFile3};

    QFile *warningFile1 = new QFile(_warningLog->_filePath[0]);
    QFile *warningFile2 = new QFile(_warningLog->_filePath[1]);
    QFile *warningFile3 = new QFile(_warningLog->_filePath[2]);
    _warningLog->_files = {warningFile1, warningFile2, warningFile3};

    setalarmLog(_alarmLog);
    setwarningLog(_warningLog);
}

void DiagnoseViewModel::DateTime()
{
    QDateTime date_time = QDateTime::currentDateTime();
    _weekday = date_time.date().toString("dddd");
    _date = date_time.date().toString("yyyy/MM/dd");
    _time = date_time.time().toString("hh:mm:ss");
}

void DiagnoseViewModel::connectOperationLod()
{
    connect(Context::instance(), &Context::addOperationLog,
            this, [=](const QVariantMap &data) {
        int channel = data.value("channel").toInt();
        auto dateTime = data.value("datetime").toDateTime();
        QString time = dateTime.toString("yyyy-MM-dd hh:mm:ss");
        QString message = data.value("message").toString();
        message = QString("【通道%1】 %2").arg(channel+1).arg(message);

        QStringList strList;
        strList << time << message;
        setOperstrList(strList);
    }
    , Qt::QueuedConnection);
}

void DiagnoseViewModel::setLabel(QModelIndex current, int currentPage, QList<SystemDiagnoseMessage> mesList)
{
    int column = current.column() / 2; // 当前诊断号列号
    setindex(PAGE_ROW * column + PAGE_ROW * 4 * currentPage + current.row());
    QString symbol = "";
    QString text = "";

    if(_index >= mesList.count()) {
        symbol.prepend(QString::number(_index) + ": ");
        symbol.append(tr("保留"));
        setsymbolLab(symbol);
        settextLab(text);
        return;
    }

    if(_index == -1) return;

    if(mesList.at(_index).format == 8) {
        for(int i = 0; i<8; ++i) {
             symbol.prepend((i < mesList.at(_index).props.bits.size()
                 ? mesList.at(_index).props.bits.at(i).value("symbol").toString() : "***"));
             if(i!=7) symbol.prepend("-");
        }

        text = (7 - _bitIndex < mesList.at(_index).props.bits.count()) ?
                mesList.at(_index).props.bits.at(7 - _bitIndex).value("text").toString() : "";
        text.prepend("BIT" + QString::number(7 - _bitIndex) + ":");
    }

    symbol = (mesList.at(_index).format == 8) ? symbol : mesList.at(_index).props.text;
    symbol.prepend(QString::number(_index) + ": ");
    setsymbolLab(symbol);

    text = (mesList.at(_index).format == 8) ? text : "";
    settextLab(text);
}

int DiagnoseViewModel::bitIndex() const
{
    return _bitIndex;
}

DiagnoseViewModel::~DiagnoseViewModel()
{
    delete _alarmLog;
    delete _warningLog;
}





