﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!--TL Control Technology Co., Ltd.-->
<!--EtherCAT CoE CiA402 Servo Device ESI file.-->
<!-- Version 20190626-->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.2">
  <Vendor>
    <Id>#x8818</Id>
    <!--0x8818/0x7217-->
    <Name LcId="1033">TLSERVO</Name>
    <Name LcId="2052">TL伺服</Name>
  </Vendor>
  <Descriptions>
    <Groups>
      <Group>
        <Type>Drive</Type>
        <Name LcId="1033">Drives</Name>
        <Name LcId="2052">驱动器</Name>
      </Group>
    </Groups>
    <Devices>
      <Device Physics="YY">
        <Type ProductCode="#x00001001" RevisionNo="#x00000000" CheckProductCode="EQ" CheckRevisionNo="NONE" CheckSerialNo="NONE">TLServoCoE</Type>
        <HideType ProductCode="#x00000000" RevisionNo="#x00000001" />
        <Name LcId="1033">TLS CoE CiA402 V0001<!--Slave Device Display Name--></Name>
        <URL LcId="1033">http://www.tlservo.com</URL>
        <Info>
          <StateMachine>
            <Timeout>
              <PreopTimeout>5000</PreopTimeout>
              <SafeopOpTimeout>10000</SafeopOpTimeout>
              <BackToInitTimeout>5000</BackToInitTimeout>
              <BackToSafeopTimeout>200</BackToSafeopTimeout>
            </Timeout>
          </StateMachine>
          <Mailbox>
            <Timeout>
              <RequestTimeout>100</RequestTimeout>
              <ResponseTimeout>3000</ResponseTimeout>
            </Timeout>
          </Mailbox>
        </Info>
        <GroupType>Drive</GroupType>
        <Profile>
          <ProfileNo>402</ProfileNo>
          <Dictionary>
            <DataTypes>
              <DataType>
                <Name>UDINT</Name>
                <BitSize>32</BitSize>
              </DataType>
              <DataType>
                <Name>UINT</Name>
                <BitSize>16</BitSize>
              </DataType>
              <DataType>
                <Name>INT</Name>
                <BitSize>16</BitSize>
              </DataType>
              <DataType>
                <Name>WORD</Name>
                <BitSize>16</BitSize>
              </DataType>
              <DataType>
                <Name>SINT</Name>
                <BitSize>8</BitSize>
              </DataType>
              <DataType>
                <Name>USINT</Name>
                <BitSize>8</BitSize>
              </DataType>
              <DataType>
                <Name>DINT</Name>
                <BitSize>32</BitSize>
              </DataType>
              <DataType>
                <Name>STRING(7)</Name>
                <BitSize>56</BitSize>
              </DataType>
              <DataType>
                <Name>STRING(8)</Name>
                <BitSize>64</BitSize>
              </DataType>
              <DataType>
                <Name>STRING(20)</Name>
                <BitSize>160</BitSize>
              </DataType>
              <DataType>
                <Name>DT1018</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Vendor Id</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Product Code</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Revision Number</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Serial Number</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1600</Name>
                <BitSize>272</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>RxPdoMapping 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>RxPdoMapping 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>RxPdoMapping 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>RxPdoMapping 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>RxPdoMapping 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>RxPdoMapping 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>RxPdoMapping 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>RxPdoMapping 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1A00</Name>
                <BitSize>272</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>TxPdoMapping 1</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>TxPdoMapping 2</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>TxPdoMapping 3</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>TxPdoMapping 4</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>TxPdoMapping 5</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>TxPdoMapping 6</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>TxPdoMapping 7</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>TxPdoMapping 8</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C00ARR</Name>
                <BaseType>USINT</BaseType>
                <BitSize>32</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>4</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C00</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C00ARR</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C10</Name>
                <BitSize>16</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C12ARR</Name>
                <BaseType>UINT</BaseType>
                <BitSize>80</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>5</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C12</Name>
                <BitSize>96</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C12ARR</Type>
                  <BitSize>80</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C13ARR</Name>
                <BaseType>UINT</BaseType>
                <BitSize>80</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>5</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT1C13</Name>
                <BitSize>96</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT1C13ARR</Type>
                  <BitSize>80</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C32</Name>
                <BitSize>176</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Synchronization type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Cycle time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Shift time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Sync Types supported</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Minimum cycle time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Cycle and copy time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT1C33</Name>
                <BitSize>176</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Synchronization type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Cycle time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Shift time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Sync Types supported</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Minimum cycle time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Calc and copy time / ns</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6046</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Vl velocity min amount</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Vl velocity max amount</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6048</Name>
                <BitSize>64</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Delta speed</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Delta time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT607BARR</Name>
                <BaseType>DINT</BaseType>
                <BitSize>64</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>2</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT607B</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Min position range limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Max position range limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT607D</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Min position limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Max position limit</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT607E</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Polarity motor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Polarity load</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT608F</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Encoder increments</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Motor revolutions</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6090</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Encoder increments per second</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Motor revolutions per second</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6091</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Motor revolutions</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Shaft revolutions</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6092</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Feed</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Shaft revolutions</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6093</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Feed constant</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6094</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6095</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6096</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6097</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Numerator</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Divisor</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT6099</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Search switch speed</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Search zero Speed</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT609a</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Acc. during search for switch</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Acc. during search for zero</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60c1</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>X1 (Interpolated Position)</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                    <PdoMapping>RT</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60C2</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Interpolation time period value</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Interpolation time index</Name>
                  <Type>SINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60c3</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Sychronize on group</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>ip sync every n event</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60c4</Name>
                <BitSize>128</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Max buffer size</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Actual buffer size</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Buffer organization</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Size of data record</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Buffer clear</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60D0</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Touch probe 1 source</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Touch probe 2 source</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60E3ARR</Name>
                <BaseType>SINT</BaseType>
                <BitSize>32</BitSize>
                <ArrayInfo>
                  <LBound>1</LBound>
                  <Elements>4</Elements>
                </ArrayInfo>
              </DataType>
              <DataType>
                <Name>DT60E3</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <Name>Elements</Name>
                  <Type>DT60E3ARR</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60f6</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Positive speed limit</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Negative speed limit</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Positive torque limit</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Negative torque limit</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60fb</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Load Home Position</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Brake control</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Brake torque add</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Following error reaction</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>m</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Safe IO State</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>m</Category>
                    <PdoMapping>RT</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Safety module state</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>RT</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT60FE</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Physical outputs</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Bitmask</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2013</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of elements</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Firmware version</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Aux firmware version</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Aux firmware sub version</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Lib Version</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2020</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>SON confirm delay</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>StandstillDelay</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2021</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Zero spd valve</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Low spd valve</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2023</Name>
                <BitSize>240</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>DriverType</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>DriverTempSensorType</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>DriverInputUn</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>DriverOutputIn</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>DriverOutputOC</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Driver Sensor Range</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Driver DC bus</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Driver Brake Res</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2024</Name>
                <BitSize>432</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>MotorType</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>MotorTempSensorType</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>MotorTempSensorInterface</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Motor Pole Pairs</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Motor Rs</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Motor Ld</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Motor Ke</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Motor Kt</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Motor Un</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Motor In</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Motor Imax</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Motor Tn</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>13</SubIdx>
                  <Name>Motor Tmax</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>336</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>14</SubIdx>
                  <Name>Motor Spdn</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>368</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>15</SubIdx>
                  <Name>Motor Spdmax</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>400</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2025</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Brake Loose confirm time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Brake Lock confirm time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2027</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Motor encoder type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Position encoder type</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2030</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Emcy deacc slope time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Emcy deacc scurve time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2031</Name>
                <BitSize>288</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Err code</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Err DC bus</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Err Irms</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Err vel cmd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Err vel fdb</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>C1D word</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>160</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>C2D word</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>192</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Para check index</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>224</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Para check Sub index</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>256</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2032</Name>
                <BitSize>448</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Err code 1st</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Err vel cmd 1st</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Err vel fdb 1st</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Err DC bus 1st</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Err Irms 1st</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Err code 2nd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>160</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Err vel cmd 2nd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Err vel fdb 2nd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Err DC bus 2nd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>Err Irms 2nd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Err code 3rd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>Err vel cmd 3rd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>320</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>13</SubIdx>
                  <Name>Err vel fdb 3rd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>352</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>14</SubIdx>
                  <Name>Err DC bus 3rd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>384</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>15</SubIdx>
                  <Name>Err Irms 3rd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>416</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2035</Name>
                <BitSize>320</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Driver over temp err valve</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Driver over temp warn valve</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Motor over temp err valve</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Motor over temp warn valve</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>DCbus over volts err valve</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>DCbus over volts warn valve</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>DCbus under volts err valve</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>DCbus under volts warn valve</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>176</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>9</SubIdx>
                  <Name>Driver over current gain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>208</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>10</SubIdx>
                  <Name>RST phase loss time valve</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>224</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>11</SubIdx>
                  <Name>Low spd follow err valve</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>240</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>12</SubIdx>
                  <Name>High spd follow err valve</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>272</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>13</SubIdx>
                  <Name>PD watch dog enable</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>304</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2040</Name>
                <BitSize>48</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Posloop Kp</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Posloop FFGain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2041</Name>
                <BitSize>96</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Spdloop Kp</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Spdloop Ki</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Spdloop cmd Limit</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Spdloop FFGain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2042</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Torqloop Kp</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Torqloop Ki</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Torqloop Cmd CCW limit</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Torqloop Cmd CW limit</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Torqloop cmd bipolar limit</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Torqloop Inertia ratio</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>Torqloop comp gain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>8</SubIdx>
                  <Name>Torqloop flux gain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2046</Name>
                <BitSize>128</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>CmdAccLowSpd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>CmdAccHighSpd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>AccSpdValve</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>DeaSpdValve</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>CmdDeaHighSpd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>CmdDeaLowSpd</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>7</SubIdx>
                  <Name>FunctionWord</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2047</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>StopInPositionKp</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>StopTargetPosition</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>StopIndexingVelocity</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>TorqueLimitOnPosition</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>StopInPositionControlWord</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>StopSlopeTime</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2049</Name>
                <BitSize>96</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>ExternalEncoderIncrements</Name>
                  <Type>UDINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>ExternalEncoderPolarity</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>VibrationControlGain</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>VibrationControlTorqueLimit</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2050</Name>
                <BitSize>112</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Spd FFCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Spd RefCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Spd FdbCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Torq FFCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Torq RefCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Pos RefCmd lpf bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2051</Name>
                <BitSize>112</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>General filter1 center</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>General filter1 Ddamping</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>General filter1 Ndamping</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>General filter2 center</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>General filter2 Ddamping</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>General filter2 Ndamping</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2052</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Trap filter1 center</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Trap filter1 bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Trap filter2 center</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Trapl filter2 bandwidth</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2060</Name>
                <BitSize>144</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Motor temp</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Driver temp</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>32</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>DCbridge temp</Name>
                  <Type>INT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>ScopeChannel0</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>ScopeChannel1</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>96</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>6</SubIdx>
                  <Name>Zero index count</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>128</BitOffs>
                  <Flags>
                    <Access>ro</Access>
                    <Category>o</Category>
                    <PdoMapping>T</PdoMapping>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2061</Name>
                <BitSize>80</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Jog spd cmd</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Jog Acc slope time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Jog Acc scurve time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>64</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
              <DataType>
                <Name>DT2062</Name>
                <BitSize>160</BitSize>
                <SubItem>
                  <SubIdx>0</SubIdx>
                  <Name>Number of entries</Name>
                  <Type>USINT</Type>
                  <BitSize>8</BitSize>
                  <BitOffs>0</BitOffs>
                </SubItem>
                <SubItem>
                  <SubIdx>1</SubIdx>
                  <Name>Internal Spdcmd 0</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>16</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>2</SubIdx>
                  <Name>Internal Spdcmd 1</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>48</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>3</SubIdx>
                  <Name>Internal Spdcmd 2</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>80</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>4</SubIdx>
                  <Name>Internal Spdcmd 3</Name>
                  <Type>DINT</Type>
                  <BitSize>32</BitSize>
                  <BitOffs>112</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
                <SubItem>
                  <SubIdx>5</SubIdx>
                  <Name>Internal SpdAcc time</Name>
                  <Type>UINT</Type>
                  <BitSize>16</BitSize>
                  <BitOffs>144</BitOffs>
                  <Flags>
                    <Access>rw</Access>
                    <Category>o</Category>
                  </Flags>
                </SubItem>
              </DataType>
            </DataTypes>
            <Objects>
              <Object>
                <Index>#x1000</Index>
                <Name>DeviceType</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Info>
                  <DefaultData>92010200</DefaultData>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>m</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1001</Index>
                <Name>ErrorRegister</Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1008</Index>
                <Name>DeviceName</Name>
                <Type>STRING(20)</Type>
                <BitSize>160</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1009</Index>
                <Name>HardwareVersion</Name>
                <Type>STRING(20)</Type>
                <BitSize>160</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x100a</Index>
                <Name>SoftwareVersion</Name>
                <Type>STRING(20)</Type>
                <BitSize>160</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1018</Index>
                <Name>Identity</Name>
                <Type>DT1018</Type>
                <BitSize>144</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1600</Index>
                <Name>RxPDO 1</Name>
                <Type>DT1600</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1601</Index>
                <Name>RxPDO 2</Name>
                <Type>DT1600</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1602</Index>
                <Name>RxPDO 3</Name>
                <Type>DT1600</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1603</Index>
                <Name>RxPDO 4</Name>
                <Type>DT1600</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1604</Index>
                <Name>RxPDO 5</Name>
                <Type>DT1600</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1A00</Index>
                <Name>TxPDO 1</Name>
                <Type>DT1A00</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1A01</Index>
                <Name>TxPDO 2</Name>
                <Type>DT1A00</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1A02</Index>
                <Name>TxPDO 3</Name>
                <Type>DT1A00</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1A03</Index>
                <Name>TxPDO 4</Name>
                <Type>DT1A00</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1A04</Index>
                <Name>TxPDO 5</Name>
                <Type>DT1A00</Type>
                <BitSize>272</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c00</Index>
                <Name>SyncManagerCommunicationType</Name>
                <Type>DT1C00</Type>
                <BitSize>48</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c12</Index>
                <Name>SyncManager2Assignment</Name>
                <Type>DT1C12</Type>
                <BitSize>96</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c13</Index>
                <Name>SyncManager3Assignment</Name>
                <Type>DT1C13</Type>
                <BitSize>96</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c32</Index>
                <Name>SyncManager2Synchronization</Name>
                <Type>DT1C32</Type>
                <BitSize>176</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x1c33</Index>
                <Name>SyncManager3Synchronization</Name>
                <Type>DT1C32</Type>
                <BitSize>176</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6007</Index>
                <Name>AbortConnectionOptionCode</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x603F</Index>
                <Name>ErrorCode</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6040</Index>
                <Name>ControlWord</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6041</Index>
                <Name>StatusWord</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6060</Index>
                <Name>ModeOfOperation</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6061</Index>
                <Name>ModeOfOperationDisplay</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6062</Index>
                <Name>PositionDemandValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6064</Index>
                <Name>PositionActualValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6065</Index>
                <Name>FollowingErrorWindow</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6066</Index>
                <Name>FollowingErrorTimeOut</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6067</Index>
                <Name>PositionWindow</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6068</Index>
                <Name>PositionWindowTime</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x606b</Index>
                <Name>VelocityDemandValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x606c</Index>
                <Name>VelocityActualValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6071</Index>
                <Name>TargetTorque</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6072</Index>
                <Name>MaxTorque</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6074</Index>
                <Name>TorqueDemandValue</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6076</Index>
                <Name>MotorRatedTorque</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6077</Index>
                <Name>TorqueActualValue</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6078</Index>
                <Name>CurrentActualValue</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6079</Index>
                <Name>DCLinkVoltage</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607a</Index>
                <Name>TargetPosition</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x607c</Index>
                <Name>HomeOffset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x607d</Index>
                <Name>SoftwarePositionLimit</Name>
                <Type>DT607D</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x607e</Index>
                <Name>Polarity</Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6089</Index>
                <Name>PositionNotationIndex</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x608a</Index>
                <Name>PositionDimensionIndex</Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x608b</Index>
                <Name>VelocityNotationIndex</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x608c</Index>
                <Name>VelocityDimensionIndex</Name>
                <Type>USINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6091</Index>
                <Name>GearRatio</Name>
                <Type>DT6091</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6098</Index>
                <Name>HomingMethod</Name>
                <Type>SINT</Type>
                <BitSize>8</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x6099</Index>
                <Name>HomingSpeeds</Name>
                <Type>DT6099</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x609a</Index>
                <Name>HomingAcceleration</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x60b0</Index>
                <Name>PositionOffset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60b1</Index>
                <Name>VelocityOffset</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60b2</Index>
                <Name>TorqueOffset</Name>
                <Type>INT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60b8</Index>
                <Name>TouchProbeFunction</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60b9</Index>
                <Name>TouchProbeStatus</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60ba</Index>
                <Name>TouchProbePosition1PositiveValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60bb</Index>
                <Name>TouchProbePosition1NegativeValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60d0</Index>
                <Name>TouchProbeSource</Name>
                <Type>DT60D0</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x60e0</Index>
                <Name>PositiveTorqueLimitValue</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60e1</Index>
                <Name>NegativeTorqueLimitValue</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60f4</Index>
                <Name>FollowingErrorActualValue</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60fd</Index>
                <Name>DigitalInputs</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                  <PdoMapping>T</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x60fe</Index>
                <Name>DigitalOutputs</Name>
                <Type>DT60FE</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x60ff</Index>
                <Name>TargetVelocity</Name>
                <Type>DINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>m</Category>
                  <PdoMapping>R</PdoMapping>
                </Flags>
              </Object>
              <Object>
                <Index>#x6502</Index>
                <Name>SupportedDriveModes</Name>
                <Type>UDINT</Type>
                <BitSize>32</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2013</Index>
                <Name>ManufacturerVersion</Name>
                <Type>DT2013</Type>
                <BitSize>144</BitSize>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2014</Index>
                <Name>ManufacturerFunction</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2015</Index>
                <Name>RTViewSelect</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2020</Index>
                <Name>DriverStateDelay</Name>
                <Type>DT2020</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2021</Index>
                <Name>MotionStateValve</Name>
                <Type>DT2021</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2023</Index>
                <Name>DriverData</Name>
                <Type>DT2023</Type>
                <BitSize>240</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>08</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2024</Index>
                <Name>MotorData</Name>
                <Type>DT2024</Type>
                <BitSize>432</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>15</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2025</Index>
                <Name>BrakeData</Name>
                <Type>DT2025</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2027</Index>
                <Name>EncoderData</Name>
                <Type>DT2027</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2030</Index>
                <Name>EmergencyStopTime</Name>
                <Type>DT2030</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2031</Index>
                <Name>ErrorStatus</Name>
                <Type>DT2031</Type>
                <BitSize>288</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>09</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2032</Index>
                <Name>ErrorHistory</Name>
                <Type>DT2032</Type>
                <BitSize>448</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>15</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2035</Index>
                <Name>SafetyValve</Name>
                <Type>DT2035</Type>
                <BitSize>320</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>13</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2040</Index>
                <Name>PosLoopControl</Name>
                <Type>DT2040</Type>
                <BitSize>48</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>02</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2041</Index>
                <Name>SpdLoopControl</Name>
                <Type>DT2041</Type>
                <BitSize>96</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2042</Index>
                <Name>TorqLoopControl</Name>
                <Type>DT2042</Type>
                <BitSize>144</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>08</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2043</Index>
                <Name>ControlLoopConfig</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2045</Index>
                <Name>DigitalInputPolarity</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2046</Index>
                <Name>CmdAccProfile</Name>
                <Type>DT2046</Type>
                <BitSize>128</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>07</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2047</Index>
                <Name>StopInPosition</Name>
                <Type>DT2047</Type>
                <BitSize>144</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>06</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2049</Index>
                <Name>FullyClosedLoop</Name>
                <Type>DT2049</Type>
                <BitSize>96</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2050</Index>
                <Name>LowPassFilter</Name>
                <Type>DT2050</Type>
                <BitSize>112</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>06</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2051</Index>
                <Name>GeneralFilter</Name>
                <Type>DT2051</Type>
                <BitSize>112</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>06</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2052</Index>
                <Name>TrapFilter</Name>
                <Type>DT2052</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>04</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2060</Index>
                <Name>RealTimeScope</Name>
                <Type>DT2060</Type>
                <BitSize>144</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>06</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2061</Index>
                <Name>JogMode</Name>
                <Type>DT2061</Type>
                <BitSize>80</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>03</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2062</Index>
                <Name>InternalVelMode</Name>
                <Type>DT2062</Type>
                <BitSize>160</BitSize>
                <Info>
                  <SubItem>
                    <Name>Number of entries</Name>
                    <Info>
                      <DefaultData>05</DefaultData>
                    </Info>
                  </SubItem>
                </Info>
                <Flags>
                  <Access>ro</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x2065</Index>
                <Name>StationAlias</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
              <Object>
                <Index>#x20FF</Index>
                <Name>ManufactoryConfigValid</Name>
                <Type>UINT</Type>
                <BitSize>16</BitSize>
                <Flags>
                  <Access>rw</Access>
                  <Category>o</Category>
                </Flags>
              </Object>
            </Objects>
          </Dictionary>
        </Profile>
        <Fmmu>Outputs</Fmmu>
        <Fmmu>Inputs</Fmmu>
        <Fmmu>MBoxState</Fmmu>
        <!-- SM0 MBX OUT (Master2Slave) -->
        <Sm MinSize="128" MaxSize="512" DefaultSize="128" StartAddress="#x1800" ControlByte="#x26" Enable="1">MBoxOut</Sm>
        <!-- SM1 MBX IN (Slave2Master) -->
        <Sm MinSize="128" MaxSize="512" DefaultSize="128" StartAddress="#x1A00" ControlByte="#x22" Enable="1">MBoxIn</Sm>
        <!-- SM2 ProcessData OUT (Master2Slave) -->
        <Sm MinSize="2" MaxSize="55" DefaultSize="6" StartAddress="#x1000" ControlByte="#x64" Enable="1">Outputs</Sm>
        <!-- SM3 ProcessData IN (Slave2Master) -->
        <Sm MinSize="2" MaxSize="55" DefaultSize="6" StartAddress="#x1100" ControlByte="#x20" Enable="1">Inputs</Sm>
        <!-- 1st RxPDO -->
        <RxPdo Fixed="0" Sm="2">
          <Index>#x1600</Index>
          <Name>1st RxPDO Mapping</Name>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <Comment>object 0x6040:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetPosition</Name>
            <Comment>object 0x607A:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </RxPdo>
        <!-- 2nd RxPDO -->
        <RxPdo Fixed="0">
          <Index>#x1601</Index>
          <Name>2nd RxPDO Mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <Comment>object 0x6040:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetVelocity</Name>
            <Comment>object 0x60FF:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </RxPdo>
        <!-- 3rd RxPDO -->
        <RxPdo Fixed="0">
          <Index>#x1602</Index>
          <Name>3rd RxPDO Mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1603</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <Comment>object 0x6040:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetPosition</Name>
            <Comment>object 0x607A:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetVelocity</Name>
            <Comment>object 0x60FF:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6071</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>TargetTorque</Name>
            <Comment>object 0x6071:0</Comment>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperation</Name>
            <Comment>object 0x6060:0</Comment>
            <DataType>SINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B8</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>TouchProbeFunction</Name>
            <Comment>object 0x60B8:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
        </RxPdo>
        <!-- 4th RxPDO -->
        <RxPdo Fixed="0">
          <Index>#x1603</Index>
          <Name>4th RxPDO Mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1604</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <Comment>object 0x6040:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetPosition</Name>
            <Comment>object 0x607A:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetVelocity</Name>
            <Comment>object 0x60FF:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperation</Name>
            <Comment>object 0x6060:0</Comment>
            <DataType>SINT</DataType>
          </Entry>
        </RxPdo>
        <!-- 5th RxPDO -->
        <RxPdo Fixed="0">
          <Index>#x1604</Index>
          <Name>5th RxPDO Mapping</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1601</Exclude>
          <Exclude>#x1602</Exclude>
          <Exclude>#x1603</Exclude>
          <Entry>
            <Index>#x6040</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Control Word</Name>
            <Comment>object 0x6040:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x607A</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetPosition</Name>
            <Comment>object 0x607A:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FF</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TargetVelocity</Name>
            <Comment>object 0x60FF:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6060</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperation</Name>
            <Comment>object 0x6060:0</Comment>
            <DataType>SINT</DataType>
          </Entry>
        </RxPdo>
        <!--1st TxPDO -->
        <TxPdo Fixed="0" Sm="3">
          <Index>#x1a00</Index>
          <Name>1st TxPDO Mapping</Name>
          <Exclude>#x1a01</Exclude>
          <Exclude>#x1a02</Exclude>
          <Exclude>#x1a03</Exclude>
          <Exclude>#x1a04</Exclude>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <Comment>object 0x6041:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>ActualPosition</Name>
            <Comment>object 0x6064:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </TxPdo>
        <!--2nd TxPDO -->
        <TxPdo Fixed="0">
          <Index>#x1a01</Index>
          <Name>2nd TxPDO Mapping</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1a02</Exclude>
          <Exclude>#x1a03</Exclude>
          <Exclude>#x1a04</Exclude>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <Comment>object 0x6041:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>ActualPosition</Name>
            <Comment>object 0x6064:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <Comment>object 0x606C:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </TxPdo>
        <!--3rd TxPDO -->
        <TxPdo Fixed="0">
          <Index>#x1a02</Index>
          <Name>3rd TxPDO Mapping</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1a01</Exclude>
          <Exclude>#x1a03</Exclude>
          <Exclude>#x1a04</Exclude>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <Comment>object 0x6041:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>ActualPosition</Name>
            <Comment>object 0x6064:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>ActualTorque</Name>
            <Comment>object 0x6077:0</Comment>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x60F4</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>FollowingErrorActualValue</Name>
            <Comment>object 0x60F4:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60FD</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>DigitalInputs</Name>
            <Comment>object 0x60FD:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperationDisplay</Name>
            <Comment>object 0x6061:0</Comment>
            <DataType>USINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60B9</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>TouchProbeStatus</Name>
            <Comment>object 0x60B9:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x60BA</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>TouchProbe1PositiveValue</Name>
            <Comment>object 0x60BA:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </TxPdo>
        <!--4th TxPDO -->
        <TxPdo Fixed="0">
          <Index>#x1a03</Index>
          <Name>4th TxPDO Mapping</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1a01</Exclude>
          <Exclude>#x1a02</Exclude>
          <Exclude>#x1a04</Exclude>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <Comment>object 0x6041:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>ActualPosition</Name>
            <Comment>object 0x6064:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <Comment>object 0x606C:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperationDisplay</Name>
            <Comment>object 0x6061:0</Comment>
            <DataType>USINT</DataType>
          </Entry>
        </TxPdo>
        <!--5th TxPDO -->
        <TxPdo Fixed="0">
          <Index>#x1a04</Index>
          <Name>5th TxPDO Mapping</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1a01</Exclude>
          <Exclude>#x1a02</Exclude>
          <Exclude>#x1a03</Exclude>
          <Entry>
            <Index>#x6041</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>Status Word</Name>
            <Comment>object 0x6041:0</Comment>
            <DataType>UINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6064</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>ActualPosition</Name>
            <Comment>object 0x6064:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x606C</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>Velocity actual value</Name>
            <Comment>object 0x606C:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6077</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>TorqueActualValue</Name>
            <Comment>object 0x6077:0</Comment>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x6061</Index>
            <SubIndex>0</SubIndex>
            <BitLen>8</BitLen>
            <Name>ModeOfOperationDisplay</Name>
            <Comment>object 0x6061:0</Comment>
            <DataType>USINT</DataType>
          </Entry>
          <Entry>
            <Index>#x6078</Index>
            <SubIndex>0</SubIndex>
            <BitLen>16</BitLen>
            <Name>CurrentActualValue</Name>
            <Comment>object 0x6078:0</Comment>
            <DataType>INT</DataType>
          </Entry>
          <Entry>
            <Index>#x6079</Index>
            <SubIndex>0</SubIndex>
            <BitLen>32</BitLen>
            <Name>DCBusVoltage</Name>
            <Comment>object 0x6079:0</Comment>
            <DataType>DINT</DataType>
          </Entry>
        </TxPdo>
        <Mailbox DataLinkLayer="1">
          <CoE SdoInfo="false" PdoConfig="true" PdoAssign="true">
            <InitCmd>
              <Transition>PS</Transition>
              <Index>#x6060</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
              <Comment>Op mode</Comment>
            </InitCmd>
          </CoE>
        </Mailbox>
        <Dc>
          <OpMode>
            <Name>DC Sync0</Name>
            <Desc>DC for Sync</Desc>
            <AssignActivate>#x0300</AssignActivate>
            <CycleTimeSync0 Factor="1">0</CycleTimeSync0>
            <ShiftTimeSync0>0</ShiftTimeSync0>
          </OpMode>
        </Dc>
        <Eeprom>
          <ByteSize>2048</ByteSize>
          <ConfigData>080E006EC8000000000000000000</ConfigData>
        </Eeprom>
        <!--Reserved-->
      </Device>
    </Devices>
  </Descriptions>
</EtherCATInfo>