<?xml version="1.0"?>
<!-- edited with XMLSpy v2011 rel. 2 sp1 (http://www.altova.com) by Tech (Maxsine) -->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.06">
	<Vendor>
		<Id>#x000007DD</Id>
		<Name>Maxsine</Name>
		<ImageData16x14>424DF6010000000000003600000028000000100000000E00000001001000000000000000000000000000000000000000000000000000FF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F1F013F011F01FF21FF7F7F3E1F01BF42FF7F9F111F013F011F013F01DF4EFF7F3F011F011F01FF25FF7F3F0D1F01FF25FF7F7F113F013F013F011F01DF4EFF7F3F5F3F011F01FF7F3F633F011F013F019F73BF6F1F011F011F015F32FF7FFF7F5F5F1F011F01FF7F7F3E1F013F011F01BF429F731F011F011F015F36FF7FFF7F5F5F1F013F01FF7F5F093F011F013F01BF19BF6F1F013F011F015F32FF7FFF7F3F631F011F013F633F011F011F011F053F01FF5A3F011F053F013F36FF7FFF7F3F633F013F019F3A1F013F015F09BF191F011F2A1F013F011F015F36FF7FFF7F5F5F1F013F017F151F011F011F2E9F461F015F0D1F011F011F013F36FF7FFF7F1F013F011F013F013F013F011F57DF773F013F011F013F011F013F01DF4EFF7F3F011F011F011F053F015F0DDF77FF7F1F2E1F013F011F013F011F05DF4EFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F</ImageData16x14>
		</Vendor>
	<Descriptions>
		<Groups>
			<Group SortOrder="0">
				<Type>ServoDrive</Type>
				<Name LcId="1033">ServoDrives</Name>
				<Image16x14>DRIVE</Image16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x01" RevisionNo="#x00001">EP3E-EC</Type>
				<Name LcId="1033">EP3E-EC</Name>
				<Info>
					<StateMachine>
						<Behavior StartToSafeopNoSync="true"/>
					</StateMachine>
				</Info>
				<GroupType>ServoDrive</GroupType>
				<Profile>
				    <ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>BOOL</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(11)</Name>
								<BitSize>88</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(6)</Name>
								<BitSize>48</BitSize>
							</DataType>
							<DataType>
								<Name>DT1010</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Store All Parameters (存储所有参数)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID (制造商)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product code (产品代码)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision number (修订号)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number (序列号)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT160X</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Mapping entry 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Mapping entry 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Mapping entry 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mapping entry 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mapping entry 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mapping entry 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Mapping entry 7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Mapping entry 8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Mapping entry 9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Mapping entry 10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A0X</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Mapping entry 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Mapping entry 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Mapping entry 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mapping entry 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mapping entry 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mapping entry 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Mapping entry 7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Mapping entry 8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Mapping entry 9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Mapping entry 10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Communication type SM0</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Communication type SM1</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Communication type SM2</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Communication type SM3</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>40</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C1X</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Index of object assigned to PDO</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync mode (同步模式)</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle time (循环时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time (位移时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync modes supported (同步模式支持)</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time (最小循环时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time (计算和复制时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time (延迟时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 time (同步时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>SM event missed counter (伺服事件错失计数器)</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter (位移过短计数器)</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error (同步错误)</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C33</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync mode (同步模式)</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle time (循环时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time (位移时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync modes supported (同步模式支持)</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time (最小循环时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time (计算和复制时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time (延迟时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 time (同步时间)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>SM event missed counter (伺服事件错失计数器)</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter (位移过短计数器)</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error (同步错误)</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2680</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>POS LOOP COM</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>POS LOOP FEEDBACK</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>POS LOOP ERROR</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2681</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>MOTOR SPEED</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2682</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>MOTOR TORQUE</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PEAK TORQUE</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>MOTOR CURRENT</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PEAK CURRENT</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position range limit (最小位置限制)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position range limit (最大位置限制)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position limit (位置限制最小值)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position limit (位置限制最大值)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Encoder Increments (编码器的增量)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor Revolutions (电机转数)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor Shaft Revolutions (电机轴转速)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions (驱动轴转速)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Feed (进给量)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions (轴旋转数)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Speed during search for switch (搜索开关的速度)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Vel.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Speed during search for zero (搜索参考位置的(较低)速度)</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Vel.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60A4</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Profile Jerk 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profile Jerk 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Profile Jerk 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Profile Jerk 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Profile Jerk 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Profile Jerk 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C1</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation data record 1(插补数据记录1)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation data record 2 (插补数据记录2)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Interpolation data record 3 (插补数据记录3)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Interpolation data record 4 (插补数据记录4)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Interpolation data record 5 (插补数据记录5)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Interpolation data record 6 (插补数据记录6)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Interpolation data record 7 (插补数据记录7)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Interpolation data record 8 (插补数据记录8)</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation time period (插补时间)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation time index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C4</Name>
								<BitSize>120</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Maximum buffer size (插补记录最大可能的数量)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Actual buffer size (插补记录的当前数量)</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Buffer organisation (缓冲区组织)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Buffer position (指定下个可用的缓冲区入口点)</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>88</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Size of data record (单位“字节”指定)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>104</BitOffs>
									<Flags>
										<Access>wo</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Buffer clear (删除缓冲)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>wo</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60E3</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Supported Homing Method 1 (支持的找零方法1)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
                                <SubItem>
									<SubIdx>2</SubIdx>
									<Name>Supported Homing Method 2 (支持的找零方法2)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Supported Homing Method 3 (支持的找零方法3)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Supported Homing Method 4 (支持的找零方法4)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>40</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries (项目数)</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Physical outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Bit mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device Type (控制器类型)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error Register (错误寄存器)</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Device Name (设备名)</Name>
								<Type>STRING(11)</Type>
								<BitSize>88</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Hardware Version (硬件版本)</Name>
								<Type>STRING(6)</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x100A</Index>
								<Name>Software Version (软件版本)</Name>
								<Type>STRING(6)</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1010</Index>
								<Name>Store Parameters (存储参数)</Name>
								<Type>DT1010</Type>
								<BitSize>48</BitSize>
								<Info>
								    <SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Store All Parameters (存储所有参数)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity Object (身份对象)</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>05</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>08006060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>2nd receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>3rd receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1603</Index>
								<Name>4th receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>0A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>08006160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A01</Index>
								<Name>2nd Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A02</Index>
								<Name>3rd Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A03</Index>
								<Name>4th Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C00</Index>
								<Name>Sync Manager Communication Type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM0</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM1</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM2</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM3</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C10</Index>
								<Name>RxPDO(SM0) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
								    <SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C11</Index>
								<Name>RxPDO(SM1) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C12</Index>
								<Name>RxPDO(SM2) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Index of object assigned to PDO</Name>
										<Info>
											<DefaultData>0116</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C13</Index>
								<Name>TxPDO(SM3) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Index of object assigned to PDO</Name>
										<Info>
											<DefaultData>011A</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C32</Index>
								<Name>Output Sync Manager Parameter (输出同步管理器参数)</Name>
								<Type>DT1C32</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode (同步模式)</Name>
										<Info>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time (循环时间)</Name>
										<Info> 
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time (位移时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported (同步模式支持)</Name>
										<Info>
											<DefaultData>0040</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time (最小循环时间)</Name>
										<Info>
											<DefaultData>400D0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time (计算和复制时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time (延迟时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 time (同步时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter (伺服事件错失计数器)</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter (位移过短计数器)</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error (同步错误)</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C33</Index>
								<Name>Input Sync Manager Parameter (输入同步管理器参数)</Name>
								<Type>DT1C33</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode (同步模式)</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time (循环时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time (位移时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported (同步模式支持)</Name>
										<Info>
											<DefaultData>0040</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time (最小循环时间)</Name>
										<Info>
											<DefaultData>400D0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time (计算和复制时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time (延迟时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 time (同步时间)</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter (伺服事件错失计数器)</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter (位移过短计数器)</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error (同步错误)</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2003</Index>
								<Name>PARA SOFTWARE VERSION (参数软件版本)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>PARA SP LOOP GAIN (第1速度环增益)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>PARA SP LOOP INTEGRAL TIME (第1速度环积分时间常数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>C800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>PARA TORQUE COM FILTER TIME (第1转矩滤波时间常数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2009</Index>
								<Name>PARA POS LOOP GAIN (第1位置环增益</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2011</Index>
								<Name>PARA LOAD INERTIA RATIO (负载转动惯量比)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2012</Index>
								<Name>PARA SP LOOP PDFF KVFR (速度环 PDFF 控制系数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2013</Index>
								<Name>PARA SPEED DETECT FILTER TIME (速度检测滤波时间常数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2015</Index>
								<Name>PARA POS FEEDFORWARD GAIN RATE (位置环前馈增益)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2016</Index>
								<Name>PARA POS FEEDFORWARD FILTER TIME (位置环前馈滤波时间常数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201B</Index>
								<Name>PARA INPULSE GEAR DEN MULTI1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1027</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201C</Index>
								<Name>PARA INPULSE GEAR DEN MULTI2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201D</Index>
								<Name>PARA INPULSE NUMERATOR1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201E</Index>
								<Name>PARA INPULSE DENOMINATOR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x202A</Index>
								<Name>PARA INPULSE INVALID TYPE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203C</Index>
								<Name>PARA SPCOM RISE TIME (速度指令加速时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203D</Index>
								<Name>PARA SPCOM FALL TIME (速度指令减速时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203F</Index>
								<Name>PARA STOP RISE FALL TIME (紧急停机的减速时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>E803</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2041</Index>
								<Name>PARA TORQUE INTERNAL CCW LIMIT (内部正转转矩限制)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2042</Index>
								<Name>PARA TORQUE INTERNAL CW LIMIT (内部反转转矩限制)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D4FE</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2043</Index>
								<Name>PARA TORQUE EXTERNAL CCW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2044</Index>
								<Name>PARA TORQUE EXTERNAL CW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9CFF</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2046</Index>
								<Name>PARA TORQUE CCW ALARM LEVEL(正转转矩过载报警水平)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2047</Index>
								<Name>PARA TORQUE CW ALARM LEVEL (反转转矩过载报警水平)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D4FE</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2048</Index>
								<Name>PARA USER TORQUE ALARM TIME (转矩过载报警检测时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204B</Index>
								<Name>PARA MAX SPEED LIMIT (最高速度限制)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>8813</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204E</Index>
								<Name>PARA TORQUE CONTROL SPEED LIMIT (转矩控制时速度限制)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>B80B</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204F</Index>
								<Name>PARA TORQUE CONTROL SPEED LIMIT ERR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>5A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2050</Index>
								<Name>PARA POS EXCEED ERROR (位置超差检测)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9001</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2054</Index>
								<Name>PARA BRAKE RESISTANCE SWITCH (制动电阻选择开关)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2055</Index>
								<Name>PARA BRAKE RESISTANCE EX VALUE (外接制动电阻的阻值)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2056</Index>
								<Name>PARA BRAKE RESISTANCE EX RATEPOWER (外接制动电阻的功率)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3C00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205A</Index>
								<Name>PARA ABS ENCODER TYPE (绝对位置编码器类型(仅绝对式))</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205D</Index>
								<Name>PARA FAN ALARM EN (风扇报警使能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205E</Index>
								<Name>PARA FAN TEMPERATURE (风扇开启温度点)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2061</Index>
								<Name>PARA CCWL CWL INVALID (忽略驱动禁止)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2100</Index>
								<Name>PARA FUNCTION DI1 (数字输入DI1功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2101</Index>
								<Name>PARA FUNCTION DI2 (数字输入DI2功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2102</Index>
								<Name>PARA FUNCTION DI3 (数字输入DI3功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2103</Index>
								<Name>PARA FUNCTION DI4 (数字输入 DI4 滤波)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2104</Index>
								<Name>PARA FUNCTION DI5 (数字输入 DI5 滤波)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210A</Index>
								<Name>PARA FILTER DI1 (数字输入DI强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210B</Index>
								<Name>PARA FILTER DI2 (数字输入D2强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210C</Index>
								<Name>PARA FILTER DI3 (数字输入D3强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210D</Index>
								<Name>PARA FILTER DI4 (数字输入D4强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210E</Index>
								<Name>PARA FILTER DI5 (数字输入D5强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2114</Index>
								<Name>PARA DI ACTIVE FORCE1 (数字输入DI强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2115</Index>
								<Name>PARA DI ACTIVE FORCE2 (数字输入D2强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2116</Index>
								<Name>PARA DI ACTIVE FORCE3 (数字输入D3强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2117</Index>
								<Name>PARA DI ACTIVE FORCE4 (数字输入D4强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2118</Index>
								<Name>PARA DI ACTIVE FORCE5 (数字输入D5强制有效)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x211E</Index>
								<Name>PARA FUNCTION DO1 (数字输出DO1功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x211F</Index>
								<Name>PARA FUNCTION DO2 (数字输出DO2功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2120</Index>
								<Name>PARA FUNCTION DO3 (数字输出DO3功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2121</Index>
								<Name>PARA FUNCTION DO4 (数字输出DO4功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2122</Index>
								<Name>PARA FUNCTION DO5 (数字输出DO5功能)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213C</Index>
								<Name>PARA SP ZERO (紧急停机的方式)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213D</Index>
								<Name>PARA SP ZERO HYSTERESIS</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213E</Index>
								<Name>PARA ZCLAMP MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213F</Index>
								<Name>PARA POS ERROR CLEAR MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2140</Index>
								<Name>PARA EMG STOP MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2141</Index>
								<Name>PARA MOTOR STOP SPEED(电机静止速度检测点)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2142</Index>
								<Name>PARA STOP BRK OFF DELAY TIME (电机静止时电磁制动器延时时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2143</Index>
								<Name>PARA RUN BRK OFF WAIT TIME (电机运转时电磁制动器等待时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2144</Index>
								<Name>PARA RUN BRK OFF SPEED (电机运转时电磁制动器动作速度)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2145</Index>
								<Name>PARA BRK OFF2ON DELAY TIME (电磁制动器打开的延迟时间)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x214E</Index>
								<Name>PARA HOME TIGGER MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x214F</Index>
								<Name>PARA HOME REF MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2150</Index>
								<Name>PARA HOME ORIGIN MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2151</Index>
								<Name>PARA HOME OFFSET HI</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2152</Index>
								<Name>PARA HOME OFFSET LO</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2153</Index>
								<Name>PARA HOME SPEED1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2154</Index>
								<Name>PARA HOME SPEED2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2155</Index>
								<Name>PARA HOME SPEED RISE TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2156</Index>
								<Name>PARA HOME SPEED FALL TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2157</Index>
								<Name>PARA HOME STOP DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2158</Index>
								<Name>PARA HOME FINISH DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2159</Index>
								<Name>PARA HOME COMMANDRUN MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2200</Index>
								<Name>PARA NOTCH FILTER FREQUENCY1  (第1共振陷波器频率)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>DC05</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2201</Index>
								<Name>PARA NOTCH FILTER Q1 (第1共振陷波器品质因数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0700</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2202</Index>
								<Name>PARA NOTCH FILTER DEEP1 (第1共振陷波器深度)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2203</Index>
								<Name>PARA NOTCH FILTER FREQUENCY2 (第2共振陷波器频率)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>DC05</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2204</Index>
								<Name>PARA NOTCH FILTER Q2 (第2共振陷波器品质因数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0700</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2205</Index>
								<Name>PARA NOTCH FILTER DEEP2 (第2共振陷波器深度)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2216</Index>
								<Name>PARA VIB COMP COFF (振动抑制的补偿系数)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2217</Index>
								<Name>PARA VIB SUPPRESS EN (振动抑制模式)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2218</Index>
								<Name>PARA VIB CYCLE SET (手动设置振动周期)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2600</Index>
								<Name>ERR CODE (驱动器错误代码)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2601</Index>
								<Name>SINGLE TURN ABSOLUTE POSITION (编码器单圈绝对位置)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2602</Index>
								<Name>MULTI TURN (编码器多圈信息)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2603</Index>
								<Name>FIRST Z EVENT FLAG</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2604</Index>
								<Name>VIBRATION PERIOD (振动周期)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2605</Index>
								<Name>DC BUS VOLTAGE (伺服驱动器直流母线电压)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2606</Index>
								<Name>POWER MODULE INTERNAL TEMPERATURE (模块内部温度)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2670</Index>
								<Name>ACCUMULATIVE LOAD RATE (累计负载率)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2671</Index>
								<Name>REGENERATIVE BRAKE LOAD RATE (再生制动负载率)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2680</Index>
								<Name>POSITION LOOP (位置环)</Name>
								<Type>DT2680</Type>
								<BitSize>112</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP COM (伺服收到的位置指令值)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP FEEDBACK (电机位置反馈值)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP ERROR (伺服位置跟踪误差)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																	
								</Flags>
							</Object>
							<Object>
								<Index>#x2681</Index>
								<Name>VELOCITY LOOP (速度环)</Name>
								<Type>DT2681</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR SPEED (伺服速度环反馈转速)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																		
								</Flags>
							</Object>
							<Object>
								<Index>#x2682</Index>
								<Name>CURRENT LOOP (电流环)</Name>
								<Type>DT2682</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR TORQUE (伺服转矩环实际转矩)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>PEAK TORQUE (伺服转矩环实际峰值转矩)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR CURRENT (伺服转矩环实际电流)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>PEAK CURRENT (伺服转矩环实际峰值电流)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																		
								</Flags>
							</Object>
							<Object>
								<Index>#x26A0</Index>
								<Name>PARA MOTOR CURRENT RMS RATE (电机额定电流)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x26A1</Index>
								<Name>PARA MOTOR TORQUE RATE (电机额定扭矩)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x26A2</Index>
								<Name>PARA MOTOR SPEED RATE (电机额定速度)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x27FE</Index>
								<Name>OPERATION COMMAND (内部操作指令)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>wo</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x27FF</Index>
								<Name>OPERATION STATUS (内部操作状态)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x6007</Index>
								<Name>Abort Connection Option Code (中止连接选项代码)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Error Code (错误故障代码)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Control Word (控制字)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Status Word (状态字)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605A</Index>
								<Name>Quick Stop Option Code (快停状态时要执行的操作)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Shutdown Option Code (操作启用状态转换到已就绪)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Disable Operation Option Code (操作启用状态转换到已启动状态时要执行的操作)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605D</Index>
								<Name>Halt Option Code (控制字6040h中设定位8(停止)时要执行的操作)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Fault Reaction Option Code (出现错误情况时电机如何停止的操作)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of Operation (所需工作模式)</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of Operation Display (当前工作模式)</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Position Demand Value (当前要求位置)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Position Actual Internal Value (当前旋转编码器位置(递增))</Name>
								<Comment LcId="1033">pulse</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position Actual Value (当前实际位置)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Following Error Window (跟随误差窗口)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Following Error Time Out (跟随误差超时)</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Position Window (位置窗口)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Position Window Time (位置窗口时间)</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Velocity Sensor Actual Value (速度传感器实际值)</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606A</Index>
								<Name>Sensor Selection Code (传感器选择代码)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606B</Index>
								<Name>Velocity Demand Value (当前要求速度)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Velocity Actual Value (当前实际速度)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606D</Index>
								<Name>Velocity Window (速度窗口)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606E</Index>
								<Name>Velocity Window Time (速度窗口时间)</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606F</Index>
								<Name>Velocity Threshold (速度阈限)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Velocity Threshold Time (速度阈限时间)</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target Torque (目标转矩)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6072</Index>
								<Name>Max Torque (最大转矩)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6073</Index>
								<Name>Max Current (最大电流)</Name>
								<Comment LcId="1033">0.1A</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6074</Index>
								<Name>Torque Demand Value (当前转矩设置值)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6075</Index>
								<Name>Motor Rated Current (电机额定电流)</Name>
								<Comment LcId="1033">A</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6076</Index>
								<Name>Motor Rated Torque (电机额定扭矩)</Name>
								<Comment LcId="1033">mNm</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque Actual Value (当前转矩值)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6078</Index>
								<Name>Current Actual Value (当前电流值)</Name>
								<Comment LcId="1033">0.1A</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6079</Index>
								<Name>DC Link Circuit Voltage (直流链路电路电压)</Name>
								<Comment LcId="1033">V</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target Position (目标位置)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607B</Index>
								<Name>Position Range Limit (位置范围限制)</Name>
								<Type>DT607B</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min position range limit (最小位置限制)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Max position range limit (最大位置限制)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x607C</Index>
								<Name>Home Offset (控制器的零位置与机器的参考点之间的差值)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Software Position Limit (相对于应用的参考点的限制位置)</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min position limit (位置限制最小值)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Max position limit (位置限制最大值)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x607E</Index>
								<Name>Polarity (改变旋转方向)</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607F</Index>
								<Name>Max Profile Velocity (最大转速)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6080</Index>
								<Name>Max Motor Speed (电机最大允许转速)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile Velocity (最大行程速度)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>End Velocity (行程斜坡结束时的速度)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Profile Acceleration (最大加速度)</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Profile Deceleration (最大减速度(减速斜坡))</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Quick Stop Deceleration (最大快停减速度)</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Motion Profile Type (斜坡类型)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Torque Slope (转矩模式下转矩的坡度)</Name>
								<Comment LcId="1033">0.1%/s</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6088</Index>
								<Name>Torque Profile Type (转矩斜坡类型)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
                            <Object>
								<Index>#x608F</Index>
								<Name>Position Encoder Resolution (位置控制的编码器/传感器的物理分辨率)</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Encoder Increments (编码器的增量)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Motor Revolutions (电机转数)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear Ratio (齿轮齿数比)</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Shaft Revolutions (电机轴转速)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions (驱动轴转速)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed Constant (进给常数)</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Feed (进给量)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions (轴旋转数)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Homing Method  (找零模式下的找零方法)</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Homing Speeds (找零模式的速度)</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed during search for switch (搜索开关的速度)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Speed during search for zero (搜索参考位置的(较低)速度)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x609A</Index>
								<Name>Homing Acceleration (找零模式的加速度斜坡)</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60A3</Index>
								<Name>Profile Jerk Use</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60A4</Index>
								<Name>Profile Jerk (加加速的限值的斜坡下的加加速大小)</Name>
								<Type>DT60A4</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 1</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 2</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 6</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B0</Index>
								<Name>Position Offset (位置偏移)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B1</Index>
								<Name>Velocity Offset (速度偏移)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B2</Index>
								<Name>Torque Offset (转矩偏移)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B8</Index>
								<Name>Touch Probe Function (探针功能)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B9</Index>
								<Name>Touch Probe Status (探针状态)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BA</Index>
								<Name>Touch Probe 1 Positive Edge Position Value (探针1正沿位置值)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BB</Index>
								<Name>Touch Probe 1 Negative Edge Position Value (探针1负沿位置值)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BC</Index>
								<Name>Touch Probe 2 Positive Edge Position Value (探针2正沿位置值)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BD</Index>
								<Name>Touch Probe 2 Negative Edge Position Value  (探针2负沿位置值)</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C0</Index>
								<Name>Interpolation Sub Mode Select (插补子模式选择)</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C1</Index>
								<Name>Interpolation Data Record (插补算法的要求位置)</Name>
								<Type>DT60C1</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>08</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 1 (插补数据记录1)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 2 (插补数据记录2)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 3 (插补数据记录3)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 4 (插补数据记录4)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 5 (插补数据记录5)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 6 (插补数据记录6)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 7 (插补数据记录7)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 8 (插补数据记录8)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C2</Index>
								<Name>Interpolation Time Period (插补时间)</Name>
								<Type>DT60C2</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation time period (插补时间)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation time index (插补时间的十的幂)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C4</Index>
								<Name>Interpolation Data Configuration (插补数据配置)</Name>
								<Type>DT60C4</Type>
								<BitSize>120</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Maximum buffer size (插补记录最大可能的数量)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Actual buffer size (插补记录的当前数量)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer organisation (缓冲区组织)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer position (指定下个可用的缓冲区入口点)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Size of data record (单位“字节”指定)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer clear (删除缓冲)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C5</Index>
								<Name>Max Acceleration (最大允许加速度)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C6</Index>
								<Name>Max Deceleration (最大允许减速度)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E0</Index>
								<Name>Positive Torque Limit Value (正转矩限制值)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E1</Index>
								<Name>Negative Torque Limit Value (负转矩限值)</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E3</Index>
								<Name>Supported Homing Methods (支持的找零方法)</Name>
								<Type>DT60E3</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 1 (支持的找零方法1)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 2 (支持的找零方法2)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 3 (支持的找零方法3)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 4 (支持的找零方法4)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F2</Index>
								<Name>Position Option Code (定位行为)</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F4</Index>
								<Name>Following Error Actual Value (当前跟随误差)</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FA</Index>
								<Name>Control Effort (定位控制器馈入转速控制器的校正转速)</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FC</Index>
								<Name>Position Demand Internal Value (当前要求位置)</Name>
								<Comment LcId="1033">pulse</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Digital Inputs (电机的数字输入)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs (电机的数字输出)</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries (项目数)</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Physical outputs (物理输出)</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Bit mask (位掩码)</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Target Velocity (目标速度)</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported Drive Modes (6060h中支持的工作模式)</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu Sm="2">Outputs</Fmmu>
				<Fmmu Sm="3">Inputs</Fmmu>
				<Fmmu>MBoxState</Fmmu>
				<Sm MinSize="32" MaxSize="192" DefaultSize="128" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="32" MaxSize="192" DefaultSize="128" StartAddress="#x1400" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="6" StartAddress="#x1800" ControlByte="#x24" Enable="1">Outputs</Sm>
				<Sm DefaultSize="6" StartAddress="#x1C00" ControlByte="#x20" Enable="1">Inputs</Sm>
				<RxPdo Fixed="0">
					<Index>#x1600</Index>
					<Name>1st Receive PDO mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of Operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0" Sm="2">
					<Index>#x1601</Index>
					<Name>2nd Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1602</Index>
					<Name>3rd Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1603</Index>
					<Name>4th Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A00</Index>
					<Name>1st Transmit PDO mapping</Name>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of Operation Display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0" Sm="3">
					<Index>#x1A01</Index>
					<Name>2nd Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A02</Index>
					<Name>3rd Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A03</Index>
					<Name>4th Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox DataLinkLayer="1">
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true"/>
				</Mailbox>
				<Dc>
				    <OpMode>
						<Name>DC ON</Name>
						<Desc>DC synchronous</Desc>
						<AssignActivate>#x300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="0">0</CycleTimeSync1>
						<ShiftTimeSync1>0</ShiftTimeSync1>
					</OpMode>
					<OpMode>
						<Name>DC OFF</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>05040B660A00</ConfigData>
				</Eeprom>
				<ImageData16x14>424DF6010000000000003600000028000000100000000E00000001001000000000000000000000000000000000000000000000000000FF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F1F013F011F01FF21FF7F7F3E1F01BF42FF7F9F111F013F011F013F01DF4EFF7F3F011F011F01FF25FF7F3F0D1F01FF25FF7F7F113F013F013F011F01DF4EFF7F3F5F3F011F01FF7F3F633F011F013F019F73BF6F1F011F011F015F32FF7FFF7F5F5F1F011F01FF7F7F3E1F013F011F01BF429F731F011F011F015F36FF7FFF7F5F5F1F013F01FF7F5F093F011F013F01BF19BF6F1F013F011F015F32FF7FFF7F3F631F011F013F633F011F011F011F053F01FF5A3F011F053F013F36FF7FFF7F3F633F013F019F3A1F013F015F09BF191F011F2A1F013F011F015F36FF7FFF7F5F5F1F013F017F151F011F011F2E9F461F015F0D1F011F011F013F36FF7FFF7F1F013F011F013F013F013F011F57DF773F013F011F013F011F013F01DF4EFF7F3F011F011F011F053F015F0DDF77FF7F1F2E1F013F011F013F011F05DF4EFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F</ImageData16x14>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>
