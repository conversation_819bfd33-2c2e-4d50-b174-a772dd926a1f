<?xml version="1.0" encoding="ISO8859-1"?>
<!-- edited with XMLSpy v2011 rel. 2 sp1 (http://www.altova.com) by Tech (Maxsine) -->
<EtherCATInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.06">
	<Vendor>
		<Id>#x000007DD</Id>
		<Name>Maxsine</Name>
		<ImageData16x14>424DF6010000000000003600000028000000100000000E00000001001000000000000000000000000000000000000000000000000000FF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F1F013F011F01FF21FF7F7F3E1F01BF42FF7F9F111F013F011F013F01DF4EFF7F3F011F011F01FF25FF7F3F0D1F01FF25FF7F7F113F013F013F011F01DF4EFF7F3F5F3F011F01FF7F3F633F011F013F019F73BF6F1F011F011F015F32FF7FFF7F5F5F1F011F01FF7F7F3E1F013F011F01BF429F731F011F011F015F36FF7FFF7F5F5F1F013F01FF7F5F093F011F013F01BF19BF6F1F013F011F015F32FF7FFF7F3F631F011F013F633F011F011F011F053F01FF5A3F011F053F013F36FF7FFF7F3F633F013F019F3A1F013F015F09BF191F011F2A1F013F011F015F36FF7FFF7F5F5F1F013F017F151F011F011F2E9F461F015F0D1F011F011F013F36FF7FFF7F1F013F011F013F013F013F011F57DF773F013F011F013F011F013F01DF4EFF7F3F011F011F011F053F015F0DDF77FF7F1F2E1F013F011F013F011F05DF4EFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F</ImageData16x14>
		</Vendor>
	<Descriptions>
		<Groups>
			<Group SortOrder="0">
				<Type>ServoDrive</Type>
				<Name LcId="1033">ServoDrives</Name>
				<Image16x14>DRIVE</Image16x14>
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x01" RevisionNo="#x00001">EP3E-EC</Type>
				<Name LcId="1033">EP3E-EC</Name>
				<Info>
					<StateMachine>
						<Behavior StartToSafeopNoSync="true"/>
					</StateMachine>
				</Info>
				<GroupType>ServoDrive</GroupType>
				<Profile>
				    <ProfileNo>402</ProfileNo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>BOOL</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(11)</Name>
								<BitSize>88</BitSize>
							</DataType>
							<DataType>
								<Name>STRING(6)</Name>
								<BitSize>48</BitSize>
							</DataType>
							<DataType>
								<Name>DT1010</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Store All Parameters</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT160X</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Mapping entry 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Mapping entry 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Mapping entry 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mapping entry 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mapping entry 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mapping entry 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Mapping entry 7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Mapping entry 8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Mapping entry 9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Mapping entry 10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A0X</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Mapping entry 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Mapping entry 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Mapping entry 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Mapping entry 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Mapping entry 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Mapping entry 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Mapping entry 7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Mapping entry 8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Mapping entry 9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Mapping entry 10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Communication type SM0</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Communication type SM1</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Communication type SM2</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Communication type SM3</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>40</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C1X</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Index of object assigned to PDO</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync modes supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C33</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Sync mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Shift time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Sync modes supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum cycle time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
										<Category>o</Category>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and copy time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 time</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">ns</Comment>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>SM event missed counter</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift too short counter</Name>
									<Type>UINT</Type>
									<Comment LcId="1033">cnt</Comment>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2680</Name>
								<BitSize>112</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>POS LOOP COM</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>POS LOOP FEEDBACK</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>POS LOOP ERROR</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2681</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>MOTOR SPEED</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2682</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>MOTOR TORQUE</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>PEAK TORQUE</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>MOTOR CURRENT</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>PEAK CURRENT</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
										<PdoMapping>T</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607B</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position range limit</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position range limit</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min position limit</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max position limit</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT608F</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Encoder Increments</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Feed</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pulse.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Driving Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Speed during search for switch</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Vel.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Speed during search for zero</Name>
									<Type>UDINT</Type>
									<Comment LcId="1033">Vel.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60A4</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Profile Jerk 1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profile Jerk 2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Profile Jerk 3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Profile Jerk 4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Profile Jerk 5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Profile Jerk 6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C1</Name>
								<BitSize>272</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation data record 1</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation data record 2</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Interpolation data record 3</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Interpolation data record 4</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Interpolation data record 5</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Interpolation data record 6</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Interpolation data record 7</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Interpolation data record 8</Name>
									<Type>DINT</Type>
									<Comment LcId="1033">Pos.Unit</Comment>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C2</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Interpolation time period</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Interpolation time index</Name>
									<Type>SINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60C4</Name>
								<BitSize>120</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Maximum Buffer Size</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Actual Buffer Size</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Buffer Organization</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Buffer Position</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>88</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Size Of Data Record</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>104</BitOffs>
									<Flags>
										<Access>wo</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Buffer Clear</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>wo</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60E3</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Supported Homing Method 1</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
                                <SubItem>
									<SubIdx>2</SubIdx>
									<Name>Supported Homing Method 2</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>24</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Supported Homing Method 3</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Supported Homing Method 4</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>40</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>Number of entries</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Physical outputs</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Bit mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>R</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device Type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error Register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Device Name</Name>
								<Type>STRING(11)</Type>
								<BitSize>88</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Hardware Version</Name>
								<Type>STRING(6)</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x100A</Index>
								<Name>Software Version</Name>
								<Type>STRING(6)</Type>
								<BitSize>48</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1010</Index>
								<Name>Store Parameters</Name>
								<Type>DT1010</Type>
								<BitSize>48</BitSize>
								<Info>
								    <SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Store All Parameters</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity Object</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1600</Index>
								<Name>1st receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>05</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>08006060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1601</Index>
								<Name>2nd receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1602</Index>
								<Name>3rd receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>2000FF60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1603</Index>
								<Name>4th receive PDO-Mapping</Name>
								<Type>DT160X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>10007160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>1st Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>0A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>08006160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A01</Index>
								<Name>2nd Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A02</Index>
								<Name>3rd Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1A03</Index>
								<Name>4th Transmit PDO Mapping</Name>
								<Type>DT1A0X</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 1</Name>
										<Info>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 2</Name>
										<Info>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 3</Name>
										<Info>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 8</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 9</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Mapping entry 10</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C00</Index>
								<Name>Sync Manager Communication Type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM0</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM1</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM2</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Communication type SM3</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C10</Index>
								<Name>RxPDO(SM0) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
								    <SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C11</Index>
								<Name>RxPDO(SM1) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C12</Index>
								<Name>RxPDO(SM2) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Index of object assigned to PDO</Name>
										<Info>
											<DefaultData>0116</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C13</Index>
								<Name>TxPDO(SM3) Assignment</Name>
								<Type>DT1C1X</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Index of object assigned to PDO</Name>
										<Info>
											<DefaultData>011A</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access WriteRestrictions="PreOP">rw</Access>
									<Category>o</Category>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C32</Index>
								<Name>Output Sync Manager Parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode</Name>
										<Info>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported</Name>
										<Info>
											<DefaultData>0040</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultData>400D0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1C33</Index>
								<Name>Input Sync Manager Parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync mode</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Cycle time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync modes supported</Name>
										<Info>
											<DefaultData>0040</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum cycle time</Name>
										<Info>
											<DefaultData>400D0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Calc and copy time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Delay time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync0 time</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SM event missed counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shift too short counter</Name>
										<Info>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync error</Name>
										<Info>
											<DefaultData>00</DefaultData>
										</Info>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2003</Index>
								<Name>PARA SOFTWARE VERSION</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>PARA SP LOOP GAIN</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>PARA SP LOOP INTEGRAL TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>C800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>PARA TORQUE COM FILTER TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2009</Index>
								<Name>PARA POS LOOP GAIN</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2011</Index>
								<Name>PARA LOAD INERTIA RATIO</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2012</Index>
								<Name>PARA SP LOOP PDFF KVFR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2013</Index>
								<Name>PARA SPEED DETECT FILTER TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2015</Index>
								<Name>PARA POS FEEDFORWARD GAIN RATE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2016</Index>
								<Name>PARA POS FEEDFORWARD FILTER TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201B</Index>
								<Name>PARA INPULSE GEAR DEN MULTI1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1027</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201C</Index>
								<Name>PARA INPULSE GEAR DEN MULTI2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201D</Index>
								<Name>PARA INPULSE NUMERATOR1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x201E</Index>
								<Name>PARA INPULSE DENOMINATOR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x202A</Index>
								<Name>PARA INPULSE INVALID TYPE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203C</Index>
								<Name>PARA SPCOM RISE TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203D</Index>
								<Name>PARA SPCOM FALL TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x203F</Index>
								<Name>PARA STOP RISE FALL TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>E803</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2041</Index>
								<Name>PARA TORQUE INTERNAL CCW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2042</Index>
								<Name>PARA TORQUE INTERNAL CW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D4FE</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2043</Index>
								<Name>PARA TORQUE EXTERNAL CCW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2044</Index>
								<Name>PARA TORQUE EXTERNAL CW LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9CFF</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2046</Index>
								<Name>PARA TORQUE CCW ALARM LEVEL</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>2C01</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2047</Index>
								<Name>PARA TORQUE CW ALARM LEVEL</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>D4FE</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2048</Index>
								<Name>PARA USER TORQUE ALARM TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204B</Index>
								<Name>PARA MAX SPEED LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>8813</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204E</Index>
								<Name>PARA TORQUE CONTROL SPEED LIMIT</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>B80B</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x204F</Index>
								<Name>PARA TORQUE CONTROL SPEED LIMIT ERR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>5A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2050</Index>
								<Name>PARA POS EXCEED ERROR</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>9001</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2054</Index>
								<Name>PARA BRAKE RESISTANCE SWITCH</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2055</Index>
								<Name>PARA BRAKE RESISTANCE EX VALUE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2056</Index>
								<Name>PARA BRAKE RESISTANCE EX RATEPOWER</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3C00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205A</Index>
								<Name>PARA ABS ENCODER TYPE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205D</Index>
								<Name>PARA FAN ALARM EN</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x205E</Index>
								<Name>PARA FAN TEMPERATURE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2061</Index>
								<Name>PARA CCWL CWL INVALID</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2100</Index>
								<Name>PARA FUNCTION DI1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2101</Index>
								<Name>PARA FUNCTION DI2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2102</Index>
								<Name>PARA FUNCTION DI3</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2103</Index>
								<Name>PARA FUNCTION DI4</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2104</Index>
								<Name>PARA FUNCTION DI5</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210A</Index>
								<Name>PARA FILTER DI1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210B</Index>
								<Name>PARA FILTER DI2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210C</Index>
								<Name>PARA FILTER DI3</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210D</Index>
								<Name>PARA FILTER DI4</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x210E</Index>
								<Name>PARA FILTER DI5</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>1400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2114</Index>
								<Name>PARA DI ACTIVE FORCE1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2115</Index>
								<Name>PARA DI ACTIVE FORCE2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2116</Index>
								<Name>PARA DI ACTIVE FORCE3</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2117</Index>
								<Name>PARA DI ACTIVE FORCE4</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2118</Index>
								<Name>PARA DI ACTIVE FORCE5</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x211E</Index>
								<Name>PARA FUNCTION DO1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x211F</Index>
								<Name>PARA FUNCTION DO2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0300</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2120</Index>
								<Name>PARA FUNCTION DO3</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2121</Index>
								<Name>PARA FUNCTION DO4</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2122</Index>
								<Name>PARA FUNCTION DO5</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213C</Index>
								<Name>PARA SP ZERO</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213D</Index>
								<Name>PARA SP ZERO HYSTERESIS</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213E</Index>
								<Name>PARA ZCLAMP MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x213F</Index>
								<Name>PARA POS ERROR CLEAR MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2140</Index>
								<Name>PARA EMG STOP MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2141</Index>
								<Name>PARA MOTOR STOP SPEED</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0500</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2142</Index>
								<Name>PARA STOP BRK OFF DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2143</Index>
								<Name>PARA RUN BRK OFF WAIT TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2144</Index>
								<Name>PARA RUN BRK OFF SPEED</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2145</Index>
								<Name>PARA BRK OFF2ON DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x214E</Index>
								<Name>PARA HOME TIGGER MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x214F</Index>
								<Name>PARA HOME REF MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2150</Index>
								<Name>PARA HOME ORIGIN MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2151</Index>
								<Name>PARA HOME OFFSET HI</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2152</Index>
								<Name>PARA HOME OFFSET LO</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2153</Index>
								<Name>PARA HOME SPEED1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2154</Index>
								<Name>PARA HOME SPEED2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2155</Index>
								<Name>PARA HOME SPEED RISE TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2156</Index>
								<Name>PARA HOME SPEED FALL TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2157</Index>
								<Name>PARA HOME STOP DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>3200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2158</Index>
								<Name>PARA HOME FINISH DELAY TIME</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2159</Index>
								<Name>PARA HOME COMMANDRUN MODE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2200</Index>
								<Name>PARA NOTCH FILTER FREQUENCY1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>DC05</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2201</Index>
								<Name>PARA NOTCH FILTER Q1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0700</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2202</Index>
								<Name>PARA NOTCH FILTER DEEP1</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2203</Index>
								<Name>PARA NOTCH FILTER FREQUENCY2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>DC05</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2204</Index>
								<Name>PARA NOTCH FILTER Q2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0700</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2205</Index>
								<Name>PARA NOTCH FILTER DEEP2</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2216</Index>
								<Name>PARA VIB COMP COFF</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0A00</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2217</Index>
								<Name>PARA VIB SUPPRESS EN</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2218</Index>
								<Name>PARA VIB CYCLE SET</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x2600</Index>
								<Name>ERR CODE</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2601</Index>
								<Name>SINGLE TURN ABSOLUTE POSITION</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2602</Index>
								<Name>MULTI TURN</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2603</Index>
								<Name>FIRST Z EVENT FLAG</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2604</Index>
								<Name>VIBRATION PERIOD</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2605</Index>
								<Name>DC BUS VOLTAGE</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2606</Index>
								<Name>POWER MODULE INTERNAL TEMPERATURE</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2670</Index>
								<Name>ACCUMULATIVE LOAD RATE</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2671</Index>
								<Name>REGENERATIVE BRAKE LOAD RATE</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x2680</Index>
								<Name>POSITION LOOP</Name>
								<Type>DT2680</Type>
								<BitSize>112</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP COM</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP FEEDBACK</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>POS LOOP ERROR</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																	
								</Flags>
							</Object>
							<Object>
								<Index>#x2681</Index>
								<Name>VELOCITY LOOP</Name>
								<Type>DT2681</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR SPEED</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																		
								</Flags>
							</Object>
							<Object>
								<Index>#x2682</Index>
								<Name>CURRENT LOOP</Name>
								<Type>DT2682</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR TORQUE</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>PEAK TORQUE</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>MOTOR CURRENT</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>PEAK CURRENT</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>																		
								</Flags>
							</Object>
							<Object>
								<Index>#x26A0</Index>
								<Name>PARA MOTOR CURRENT RMS RATE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x26A1</Index>
								<Name>PARA MOTOR TORQUE RATE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x26A2</Index>
								<Name>PARA MOTOR SPEED RATE</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x27FE</Index>
								<Name>OPERATION COMMAND</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>wo</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x27FF</Index>
								<Name>OPERATION STATUS</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>									
								</Flags>
							</Object>
							<Object>
								<Index>#x6007</Index>
								<Name>Abort Connection Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Error Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Control Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Status Word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605A</Index>
								<Name>Quick Stop Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Shutdown Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Disable Operation Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605D</Index>
								<Name>Halt Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Fault Reaction Option Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Modes of Operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Modes of Operation Display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Position Demand Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Position Actual Internal Value</Name>
								<Comment LcId="1033">pulse</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Position Actual Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Following Error Window</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Following Error Time Out</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Position Window</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Position Window Time</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Velocity Sensor Actual Value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606A</Index>
								<Name>Sensor Selection Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606B</Index>
								<Name>Velocity Demand Value</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Velocity Actual Value</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606D</Index>
								<Name>Velocity Window</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606E</Index>
								<Name>Velocity Window Time</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x606F</Index>
								<Name>Velocity Threshold</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Velocity Threshold Time</Name>
								<Comment LcId="1033">ms</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Target Torque</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6072</Index>
								<Name>Max Torque</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6073</Index>
								<Name>Max Current</Name>
								<Comment LcId="1033">0.1A</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6074</Index>
								<Name>Torque Demand Value</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6075</Index>
								<Name>Motor Rated Current</Name>
								<Comment LcId="1033">A</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6076</Index>
								<Name>Motor Rated Torque</Name>
								<Comment LcId="1033">mNm</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Torque Actual Value</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6078</Index>
								<Name>Current Actual Value</Name>
								<Comment LcId="1033">0.1A</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6079</Index>
								<Name>DC Link Circuit Voltage</Name>
								<Comment LcId="1033">V</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Target Position</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607B</Index>
								<Name>Position Range Limit</Name>
								<Type>DT607B</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min position range limit</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Max position range limit</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x607C</Index>
								<Name>Home Offset</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Software Position Limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min position limit</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Max position limit</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x607E</Index>
								<Name>Polarity</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607F</Index>
								<Name>Max Profile Velocity</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6080</Index>
								<Name>Max Motor Speed</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Profile Velocity</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>End Velocity</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Profile Acceleration</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Profile Deceleration</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Quick Stop Deceleration</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Motion Profile Type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>									
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Torque Slope</Name>
								<Comment LcId="1033">0.1%/s</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6088</Index>
								<Name>Torque Profile Type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
                            <Object>
								<Index>#x608F</Index>
								<Name>Position Encoder Resolution</Name>
								<Type>DT608F</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Encoder Increments</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Motor Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Gear Ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Shaft Revolutions</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Feed Constant</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Feed</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Driving Shaft Revolutions</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Homing Method</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Homing Speeds</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed during search for switch</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Speed during search for zero</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x609A</Index>
								<Name>Homing Acceleration</Name>
								<Comment LcId="1033">Acc.Unit</Comment>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60A3</Index>
								<Name>Profile Jerk Use</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60A4</Index>
								<Name>Profile Jerk</Name>
								<Type>DT60A4</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 1</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 2</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 6</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B0</Index>
								<Name>Position Offset</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B1</Index>
								<Name>Velocity Offset</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B2</Index>
								<Name>Torque Offset</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B8</Index>
								<Name>Touch Probe Function</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B9</Index>
								<Name>Touch Probe Status</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BA</Index>
								<Name>Touch Probe 1 Positive Edge Position Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BB</Index>
								<Name>Touch Probe 1 Negative Edge Position Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BC</Index>
								<Name>Touch Probe 2 Positive Edge Position Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60BD</Index>
								<Name>Touch Probe 2 Negative Edge Position Value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C0</Index>
								<Name>Interpolation Sub Mode Select</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C1</Index>
								<Name>Interpolation Data Record</Name>
								<Type>DT60C1</Type>
								<BitSize>272</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>08</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 1</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 2</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 4</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 5</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 6</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 7</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation data record 8</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C2</Index>
								<Name>Interpolation Time Period</Name>
								<Type>DT60C2</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Interpolation time period</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Interpolation time index</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C4</Index>
								<Name>Interpolation Data Configuration</Name>
								<Type>DT60C4</Type>
								<BitSize>120</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Maximum Buffer Size</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Actual Buffer Size</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer Organization</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer Position</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Size Of Data Record</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Buffer Clear</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C5</Index>
								<Name>Max Acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C6</Index>
								<Name>Max Deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E0</Index>
								<Name>Positive Torque Limit Value</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E1</Index>
								<Name>Negative Torque Limit Value</Name>
								<Comment LcId="1033">0.1%</Comment>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E3</Index>
								<Name>Supported Homing Methods</Name>
								<Type>DT60E3</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 1</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 2</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 3</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Supported Homing Method 4</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F2</Index>
								<Name>Position Option Code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F4</Index>
								<Name>Following Error Actual Value</Name>
								<Comment LcId="1033">Pos.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FA</Index>
								<Name>Control Effort</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FC</Index>
								<Name>Position Demand Internal Value</Name>
								<Comment LcId="1033">pulse</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Digital Inputs</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>T</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Digital outputs</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>Number of entries</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Physical outputs</Name>
										<Info/>
									</SubItem>
									<SubItem>
										<Name>Bit mask</Name>
										<Info/>
									</SubItem>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Target Velocity</Name>
								<Comment LcId="1033">Vel.Unit</Comment>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>R</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported Drive Modes</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu Sm="2">Outputs</Fmmu>
				<Fmmu Sm="3">Inputs</Fmmu>
				<Fmmu>MBoxState</Fmmu>
				<Sm MinSize="32" MaxSize="192" DefaultSize="128" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="32" MaxSize="192" DefaultSize="128" StartAddress="#x1400" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="6" StartAddress="#x1800" ControlByte="#x24" Enable="1">Outputs</Sm>
				<Sm DefaultSize="6" StartAddress="#x1C00" ControlByte="#x20" Enable="1">Inputs</Sm>
				<RxPdo Fixed="0">
					<Index>#x1600</Index>
					<Name>1st Receive PDO mapping</Name>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6060</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of Operation</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0" Sm="2">
					<Index>#x1601</Index>
					<Name>2nd Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1602</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Position</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1602</Index>
					<Name>3rd Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1603</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60FF</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Target Velocity</Name>
						<DataType>DINT</DataType>
					</Entry>
				</RxPdo>
				<RxPdo Fixed="0">
					<Index>#x1603</Index>
					<Name>4th Receive PDO mapping</Name>
					<Exclude>#x1600</Exclude>
					<Exclude>#x1601</Exclude>
					<Exclude>#x1602</Exclude>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Controlword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6071</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Target Torque</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A00</Index>
					<Name>1st Transmit PDO mapping</Name>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Mode of Operation Display</Name>
						<DataType>SINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0" Sm="3">
					<Index>#x1A01</Index>
					<Name>2nd Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A02</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A02</Index>
					<Name>3rd Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A03</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Velocity Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
				</TxPdo>
				<TxPdo Fixed="0">
					<Index>#x1A03</Index>
					<Name>4th Transmit PDO mapping</Name>
					<Exclude>#x1A00</Exclude>
					<Exclude>#x1A01</Exclude>
					<Exclude>#x1A02</Exclude>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Position Actual Value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Torque Actual Value</Name>
						<DataType>INT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox DataLinkLayer="1">
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true"/>
				</Mailbox>
				<Dc>
				    <OpMode>
						<Name>DC ON</Name>
						<Desc>DC synchronous</Desc>
						<AssignActivate>#x300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<ShiftTimeSync0>0</ShiftTimeSync0>
						<CycleTimeSync1 Factor="0">0</CycleTimeSync1>
						<ShiftTimeSync1>0</ShiftTimeSync1>
					</OpMode>
					<OpMode>
						<Name>DC OFF</Name>
						<Desc>DC unused</Desc>
						<AssignActivate>#x0000</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>05040B660A00</ConfigData>
				</Eeprom>
				<ImageData16x14>424DF6010000000000003600000028000000100000000E00000001001000000000000000000000000000000000000000000000000000FF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F1F013F011F01FF21FF7F7F3E1F01BF42FF7F9F111F013F011F013F01DF4EFF7F3F011F011F01FF25FF7F3F0D1F01FF25FF7F7F113F013F013F011F01DF4EFF7F3F5F3F011F01FF7F3F633F011F013F019F73BF6F1F011F011F015F32FF7FFF7F5F5F1F011F01FF7F7F3E1F013F011F01BF429F731F011F011F015F36FF7FFF7F5F5F1F013F01FF7F5F093F011F013F01BF19BF6F1F013F011F015F32FF7FFF7F3F631F011F013F633F011F011F011F053F01FF5A3F011F053F013F36FF7FFF7F3F633F013F019F3A1F013F015F09BF191F011F2A1F013F011F015F36FF7FFF7F5F5F1F013F017F151F011F011F2E9F461F015F0D1F011F011F013F36FF7FFF7F1F013F011F013F013F013F011F57DF773F013F011F013F011F013F01DF4EFF7F3F011F011F011F053F015F0DDF77FF7F1F2E1F013F011F013F011F05DF4EFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7FFF7F</ImageData16x14>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>
